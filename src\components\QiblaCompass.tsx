'use client'

import { useState, useEffect } from 'react'
import { Compass, Navigation, MapPin, RefreshCw } from 'lucide-react'

interface QiblaCompassProps {
  latitude?: number
  longitude?: number
}

export function QiblaCompass({ latitude = 24.7136, longitude = 46.6753 }: QiblaCompassProps) {
  const [qiblaDirection, setQiblaDirection] = useState<number>(0)
  const [deviceHeading, setDeviceHeading] = useState<number>(0)
  const [isSupported, setIsSupported] = useState<boolean>(false)
  const [permission, setPermission] = useState<string>('unknown')
  const [isLoading, setIsLoading] = useState<boolean>(true)

  // إحداثيات الكعبة المشرفة
  const KAABA_LAT = 21.422487
  const KAABA_LNG = 39.826206

  // حساب اتجاه القبلة
  const calculateQiblaDirection = (lat: number, lng: number): number => {
    const latRad = (lat * Math.PI) / 180
    const lngRad = (lng * Math.PI) / 180
    const kaabaLatRad = (KAABA_LAT * Math.PI) / 180
    const kaabaLngRad = (KAABA_LNG * Math.PI) / 180

    const dLng = kaabaLngRad - lngRad

    const y = Math.sin(dLng) * Math.cos(kaabaLatRad)
    const x = Math.cos(latRad) * Math.sin(kaabaLatRad) - 
              Math.sin(latRad) * Math.cos(kaabaLatRad) * Math.cos(dLng)

    let bearing = Math.atan2(y, x)
    bearing = (bearing * 180) / Math.PI
    bearing = (bearing + 360) % 360

    return bearing
  }

  // حساب المسافة إلى مكة
  const calculateDistanceToMecca = (lat: number, lng: number): number => {
    const R = 6371 // نصف قطر الأرض بالكيلومتر
    const dLat = ((KAABA_LAT - lat) * Math.PI) / 180
    const dLng = ((KAABA_LNG - lng) * Math.PI) / 180
    
    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
              Math.cos((lat * Math.PI) / 180) * Math.cos((KAABA_LAT * Math.PI) / 180) *
              Math.sin(dLng / 2) * Math.sin(dLng / 2)
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
    return R * c
  }

  useEffect(() => {
    // حساب اتجاه القبلة
    const qibla = calculateQiblaDirection(latitude, longitude)
    setQiblaDirection(qibla)

    // التحقق من دعم البوصلة
    if ('DeviceOrientationEvent' in window) {
      setIsSupported(true)
      
      // طلب الإذن للوصول للبوصلة (iOS 13+)
      if (typeof (DeviceOrientationEvent as any).requestPermission === 'function') {
        (DeviceOrientationEvent as any).requestPermission()
          .then((response: string) => {
            setPermission(response)
            if (response === 'granted') {
              startCompass()
            }
            setIsLoading(false)
          })
          .catch(() => {
            setPermission('denied')
            setIsLoading(false)
          })
      } else {
        setPermission('granted')
        startCompass()
        setIsLoading(false)
      }
    } else {
      setIsSupported(false)
      setIsLoading(false)
    }

    return () => {
      stopCompass()
    }
  }, [latitude, longitude])

  const startCompass = () => {
    const handleOrientation = (event: DeviceOrientationEvent) => {
      if (event.alpha !== null) {
        setDeviceHeading(360 - event.alpha)
      }
    }

    window.addEventListener('deviceorientation', handleOrientation)
    
    return () => {
      window.removeEventListener('deviceorientation', handleOrientation)
    }
  }

  const stopCompass = () => {
    // إيقاف البوصلة إذا لزم الأمر
  }

  const requestPermission = async () => {
    if (typeof (DeviceOrientationEvent as any).requestPermission === 'function') {
      try {
        const response = await (DeviceOrientationEvent as any).requestPermission()
        setPermission(response)
        if (response === 'granted') {
          startCompass()
        }
      } catch (error) {
        setPermission('denied')
      }
    }
  }

  const distance = calculateDistanceToMecca(latitude, longitude)
  const qiblaAngle = qiblaDirection - deviceHeading

  if (isLoading) {
    return (
      <div className="text-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto"></div>
        <p className="mt-2 text-gray-600 dark:text-gray-400">جاري تحميل البوصلة...</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Compass */}
      <div className="relative">
        <div className="w-64 h-64 mx-auto relative">
          {/* Compass Background */}
          <div className="w-full h-full rounded-full border-4 border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 shadow-lg relative overflow-hidden">
            {/* Compass Rose */}
            <div 
              className="absolute inset-0 transition-transform duration-300 ease-out"
              style={{ transform: `rotate(${deviceHeading}deg)` }}
            >
              {/* North Indicator */}
              <div className="absolute top-2 left-1/2 transform -translate-x-1/2 text-red-600 font-bold text-sm">
                N
              </div>
              {/* South Indicator */}
              <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 text-gray-600 font-bold text-sm">
                S
              </div>
              {/* East Indicator */}
              <div className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-600 font-bold text-sm">
                E
              </div>
              {/* West Indicator */}
              <div className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-600 font-bold text-sm">
                W
              </div>
              
              {/* Degree Markings */}
              {Array.from({ length: 36 }, (_, i) => i * 10).map((degree) => (
                <div
                  key={degree}
                  className="absolute w-0.5 h-4 bg-gray-400 top-0 left-1/2 transform -translate-x-1/2 origin-bottom"
                  style={{
                    transform: `translateX(-50%) rotate(${degree}deg)`,
                    transformOrigin: '50% 128px'
                  }}
                />
              ))}
            </div>

            {/* Qibla Direction Arrow */}
            <div 
              className="absolute inset-0 transition-transform duration-300 ease-out"
              style={{ transform: `rotate(${qiblaAngle}deg)` }}
            >
              <div className="absolute top-4 left-1/2 transform -translate-x-1/2">
                <div className="w-0 h-0 border-l-4 border-r-4 border-b-8 border-l-transparent border-r-transparent border-b-green-600"></div>
                <div className="w-1 h-20 bg-green-600 mx-auto"></div>
              </div>
            </div>

            {/* Center Dot */}
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-3 h-3 bg-gray-800 dark:bg-white rounded-full"></div>
            
            {/* Kaaba Icon */}
            <div 
              className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 transition-transform duration-300 ease-out"
              style={{ transform: `translate(-50%, -50%) rotate(${qiblaAngle}deg) translateY(-40px)` }}
            >
              <div className="w-6 h-6 bg-green-600 rounded-sm flex items-center justify-center">
                <div className="w-3 h-3 bg-white rounded-sm"></div>
              </div>
            </div>
          </div>
        </div>

        {/* Direction Info */}
        <div className="text-center mt-4">
          <div className="text-2xl font-bold text-green-600 dark:text-green-400 mb-2">
            {Math.round(qiblaDirection)}°
          </div>
          <p className="text-gray-600 dark:text-gray-400">
            اتجاه القبلة من موقعك
          </p>
        </div>
      </div>

      {/* Status and Info */}
      <div className="space-y-4">
        {!isSupported && (
          <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
            <div className="flex items-center space-x-2 space-x-reverse">
              <Compass className="h-5 w-5 text-yellow-600 dark:text-yellow-400" />
              <p className="text-yellow-800 dark:text-yellow-200">
                البوصلة غير مدعومة في هذا الجهاز
              </p>
            </div>
          </div>
        )}

        {isSupported && permission === 'denied' && (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2 space-x-reverse">
                <Navigation className="h-5 w-5 text-red-600 dark:text-red-400" />
                <p className="text-red-800 dark:text-red-200">
                  يجب السماح بالوصول للبوصلة
                </p>
              </div>
              <button
                onClick={requestPermission}
                className="px-3 py-1 bg-red-600 text-white rounded-md hover:bg-red-700 text-sm"
              >
                السماح
              </button>
            </div>
          </div>
        )}

        {/* Location Info */}
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div className="flex items-center space-x-2 space-x-reverse">
              <MapPin className="h-4 w-4 text-gray-600 dark:text-gray-400" />
              <span className="text-gray-700 dark:text-gray-300">
                الموقع: {latitude.toFixed(4)}, {longitude.toFixed(4)}
              </span>
            </div>
            <div className="flex items-center space-x-2 space-x-reverse">
              <Compass className="h-4 w-4 text-gray-600 dark:text-gray-400" />
              <span className="text-gray-700 dark:text-gray-300">
                المسافة إلى مكة: {Math.round(distance)} كم
              </span>
            </div>
          </div>
        </div>

        {/* Instructions */}
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
          <h4 className="font-semibold text-blue-800 dark:text-blue-200 mb-2">
            تعليمات الاستخدام:
          </h4>
          <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
            <li>• امسك الجهاز بشكل مسطح</li>
            <li>• تأكد من عدم وجود معادن قريبة</li>
            <li>• السهم الأخضر يشير إلى اتجاه القبلة</li>
            <li>• اتجه نحو السهم الأخضر للصلاة</li>
          </ul>
        </div>
      </div>
    </div>
  )
}
