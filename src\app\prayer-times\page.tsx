import { Clock, MapPin, Bell, Calendar, Settings, Compass } from 'lucide-react'
import { AdvancedPrayerTimes } from '@/components/AdvancedPrayerTimes'
import { QiblaCompass } from '@/components/QiblaCompass'

export default function PrayerTimesPage() {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center py-6">
        <h1 className="text-3xl md:text-4xl font-bold text-gray-800 dark:text-white mb-4">
          أوقات الصلاة
        </h1>
        <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
          مواقيت الصلاة الدقيقة حسب موقعك الجغرافي مع التنبيهات والتذكيرات
        </p>
      </div>

      {/* Current Prayer Times */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-6 text-center">
          مواقيت اليوم
        </h2>
        <AdvancedPrayerTimes />
      </div>

      {/* Weekly Calendar */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
        <div className="flex items-center space-x-3 space-x-reverse mb-6">
          <Calendar className="h-6 w-6 text-blue-600 dark:text-blue-400" />
          <h2 className="text-2xl font-bold text-gray-800 dark:text-white">
            التقويم الأسبوعي
          </h2>
        </div>

        <div className="grid grid-cols-7 gap-2 mb-4">
          {['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'].map((day, index) => (
            <div key={day} className="text-center p-2">
              <div className={`text-sm font-medium ${index === 5 ? 'text-green-600 dark:text-green-400' : 'text-gray-600 dark:text-gray-400'}`}>
                {day}
              </div>
            </div>
          ))}
        </div>

        <div className="text-center text-gray-600 dark:text-gray-400">
          <p>سيتم إضافة التقويم الأسبوعي قريباً إن شاء الله</p>
        </div>
      </div>

      {/* Prayer Settings */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Location Settings */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
          <div className="flex items-center space-x-3 space-x-reverse mb-4">
            <MapPin className="h-6 w-6 text-blue-600 dark:text-blue-400" />
            <h3 className="text-xl font-bold text-gray-800 dark:text-white">
              إعدادات الموقع
            </h3>
          </div>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                المدينة الحالية
              </label>
              <select className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                <option>الرياض</option>
                <option>جدة</option>
                <option>مكة المكرمة</option>
                <option>المدينة المنورة</option>
                <option>الدمام</option>
                <option>أخرى...</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                طريقة الحساب
              </label>
              <select className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                <option>أم القرى - مكة المكرمة</option>
                <option>الهيئة العامة للمساحة</option>
                <option>رابطة العالم الإسلامي</option>
                <option>الجامعة الإسلامية - كراتشي</option>
              </select>
            </div>

            <button className="w-full bg-blue-600 text-white py-3 rounded-lg hover:bg-blue-700 transition-colors duration-200">
              حفظ الإعدادات
            </button>
          </div>
        </div>

        {/* Notification Settings */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
          <div className="flex items-center space-x-3 space-x-reverse mb-4">
            <Bell className="h-6 w-6 text-green-600 dark:text-green-400" />
            <h3 className="text-xl font-bold text-gray-800 dark:text-white">
              إعدادات التنبيهات
            </h3>
          </div>

          <div className="space-y-4">
            {['الفجر', 'الظهر', 'العصر', 'المغرب', 'العشاء'].map((prayer) => (
              <div key={prayer} className="flex items-center justify-between">
                <span className="text-gray-700 dark:text-gray-300">{prayer}</span>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input type="checkbox" className="sr-only peer" defaultChecked />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                </label>
              </div>
            ))}

            <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between mb-2">
                <span className="text-gray-700 dark:text-gray-300">تشغيل الأذان</span>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input type="checkbox" className="sr-only peer" defaultChecked />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                </label>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-gray-700 dark:text-gray-300">التذكير قبل الصلاة</span>
                <select className="p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm">
                  <option>5 دقائق</option>
                  <option>10 دقائق</option>
                  <option>15 دقائق</option>
                  <option>30 دقيقة</option>
                </select>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Qibla Direction */}
      <div className="bg-gradient-to-r from-green-50 to-blue-50 dark:from-gray-800 dark:to-gray-700 rounded-xl shadow-lg p-6">
        <div className="text-center mb-6">
          <h3 className="text-2xl font-bold text-gray-800 dark:text-white mb-2">
            اتجاه القبلة
          </h3>
          <p className="text-gray-600 dark:text-gray-300">
            بوصلة دقيقة لتحديد اتجاه القبلة من موقعك
          </p>
        </div>
        <QiblaCompass />
      </div>
    </div>
  )
}
