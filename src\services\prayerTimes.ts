export interface PrayerTime {
  name: string
  time: string
  arabic: string
  timestamp: number
}

export interface PrayerTimesData {
  date: string
  location: string
  coordinates: {
    latitude: number
    longitude: number
  }
  prayers: PrayerTime[]
  nextPrayer: {
    name: string
    timeRemaining: string
    timestamp: number
  } | null
}

export interface Location {
  name: string
  country: string
  coordinates: {
    latitude: number
    longitude: number
  }
  timezone: string
}

// مواقع مشهورة في العالم الإسلامي
export const popularLocations: Location[] = [
  {
    name: 'الرياض',
    country: 'السعودية',
    coordinates: { latitude: 24.7136, longitude: 46.6753 },
    timezone: 'Asia/Riyadh'
  },
  {
    name: 'مكة المكرمة',
    country: 'السعودية',
    coordinates: { latitude: 21.3891, longitude: 39.8579 },
    timezone: 'Asia/Riyadh'
  },
  {
    name: 'المدينة المنورة',
    country: 'السعودية',
    coordinates: { latitude: 24.5247, longitude: 39.5692 },
    timezone: 'Asia/Riyadh'
  },
  {
    name: 'جدة',
    country: 'السعودية',
    coordinates: { latitude: 21.2854, longitude: 39.2376 },
    timezone: 'Asia/Riyadh'
  },
  {
    name: 'الدمام',
    country: 'السعودية',
    coordinates: { latitude: 26.4207, longitude: 50.0888 },
    timezone: 'Asia/Riyadh'
  },
  {
    name: 'القاهرة',
    country: 'مصر',
    coordinates: { latitude: 30.0444, longitude: 31.2357 },
    timezone: 'Africa/Cairo'
  },
  {
    name: 'دبي',
    country: 'الإمارات',
    coordinates: { latitude: 25.2048, longitude: 55.2708 },
    timezone: 'Asia/Dubai'
  },
  {
    name: 'الكويت',
    country: 'الكويت',
    coordinates: { latitude: 29.3117, longitude: 47.4818 },
    timezone: 'Asia/Kuwait'
  },
  {
    name: 'الدوحة',
    country: 'قطر',
    coordinates: { latitude: 25.2854, longitude: 51.5310 },
    timezone: 'Asia/Qatar'
  },
  {
    name: 'إسطنبول',
    country: 'تركيا',
    coordinates: { latitude: 41.0082, longitude: 28.9784 },
    timezone: 'Europe/Istanbul'
  }
]

// طرق حساب أوقات الصلاة
export const calculationMethods = [
  {
    id: 'mwl',
    name: 'رابطة العالم الإسلامي',
    fajrAngle: 18,
    ishaAngle: 17
  },
  {
    id: 'isna',
    name: 'الجمعية الإسلامية لأمريكا الشمالية',
    fajrAngle: 15,
    ishaAngle: 15
  },
  {
    id: 'egypt',
    name: 'الهيئة العامة للمساحة المصرية',
    fajrAngle: 19.5,
    ishaAngle: 17.5
  },
  {
    id: 'makkah',
    name: 'أم القرى - مكة المكرمة',
    fajrAngle: 18.5,
    ishaAngle: 90 // 90 دقيقة بعد المغرب
  },
  {
    id: 'karachi',
    name: 'الجامعة الإسلامية - كراتشي',
    fajrAngle: 18,
    ishaAngle: 18
  }
]

// حساب أوقات الصلاة (خوارزمية مبسطة)
export function calculatePrayerTimes(
  latitude: number,
  longitude: number,
  date: Date = new Date(),
  methodId: string = 'mwl'
): PrayerTime[] {
  // هذه خوارزمية مبسطة للعرض
  // في التطبيق الحقيقي سنستخدم مكتبة متخصصة مثل adhan-js
  
  const method = calculationMethods.find(m => m.id === methodId) || calculationMethods[0]
  
  // حساب الشروق والغروب (مبسط)
  const dayOfYear = Math.floor((date.getTime() - new Date(date.getFullYear(), 0, 0).getTime()) / 86400000)
  const solarDeclination = 23.45 * Math.sin((360 * (284 + dayOfYear) / 365) * Math.PI / 180)
  
  const latRad = latitude * Math.PI / 180
  const declRad = solarDeclination * Math.PI / 180
  
  const hourAngle = Math.acos(-Math.tan(latRad) * Math.tan(declRad)) * 180 / Math.PI
  
  // أوقات تقريبية (يجب استخدام حسابات دقيقة في التطبيق الحقيقي)
  const sunrise = 12 - hourAngle / 15 - longitude / 15 + 4 / 60
  const sunset = 12 + hourAngle / 15 - longitude / 15 + 4 / 60
  
  const fajr = sunrise - method.fajrAngle / 15
  const dhuhr = 12 - longitude / 15 + 4 / 60
  const asr = dhuhr + (Math.atan(1 + Math.tan(Math.abs(latitude - solarDeclination) * Math.PI / 180)) * 180 / Math.PI) / 15
  const maghrib = sunset
  const isha = typeof method.ishaAngle === 'number' && method.ishaAngle > 50 
    ? maghrib + method.ishaAngle / 60 // دقائق بعد المغرب
    : sunset + method.ishaAngle / 15 // زاوية

  const formatTime = (hours: number): string => {
    const h = Math.floor(hours) % 24
    const m = Math.floor((hours % 1) * 60)
    return `${h.toString().padStart(2, '0')}:${m.toString().padStart(2, '0')}`
  }

  const getTimestamp = (hours: number): number => {
    const h = Math.floor(hours) % 24
    const m = Math.floor((hours % 1) * 60)
    const today = new Date()
    today.setHours(h, m, 0, 0)
    return today.getTime()
  }

  return [
    {
      name: 'الفجر',
      time: formatTime(fajr),
      arabic: 'الفجر',
      timestamp: getTimestamp(fajr)
    },
    {
      name: 'الشروق',
      time: formatTime(sunrise),
      arabic: 'الشروق',
      timestamp: getTimestamp(sunrise)
    },
    {
      name: 'الظهر',
      time: formatTime(dhuhr),
      arabic: 'الظهر',
      timestamp: getTimestamp(dhuhr)
    },
    {
      name: 'العصر',
      time: formatTime(asr),
      arabic: 'العصر',
      timestamp: getTimestamp(asr)
    },
    {
      name: 'المغرب',
      time: formatTime(maghrib),
      arabic: 'المغرب',
      timestamp: getTimestamp(maghrib)
    },
    {
      name: 'العشاء',
      time: formatTime(isha),
      arabic: 'العشاء',
      timestamp: getTimestamp(isha)
    }
  ]
}

// حساب الصلاة القادمة
export function getNextPrayer(prayers: PrayerTime[]): {
  name: string
  timeRemaining: string
  timestamp: number
} | null {
  const now = Date.now()
  const prayerTimes = prayers.filter(p => p.name !== 'الشروق') // استبعاد الشروق
  
  for (const prayer of prayerTimes) {
    if (prayer.timestamp > now) {
      const timeDiff = prayer.timestamp - now
      const hours = Math.floor(timeDiff / (1000 * 60 * 60))
      const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60))
      
      return {
        name: prayer.name,
        timeRemaining: `${hours}:${minutes.toString().padStart(2, '0')}`,
        timestamp: prayer.timestamp
      }
    }
  }
  
  // إذا انتهى اليوم، الصلاة القادمة هي فجر اليوم التالي
  const fajr = prayerTimes[0]
  const tomorrow = new Date(fajr.timestamp)
  tomorrow.setDate(tomorrow.getDate() + 1)
  
  const timeDiff = tomorrow.getTime() - now
  const hours = Math.floor(timeDiff / (1000 * 60 * 60))
  const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60))
  
  return {
    name: fajr.name,
    timeRemaining: `${hours}:${minutes.toString().padStart(2, '0')}`,
    timestamp: tomorrow.getTime()
  }
}

// الحصول على أوقات الصلاة لموقع معين
export async function getPrayerTimesForLocation(
  location: Location,
  date: Date = new Date(),
  methodId: string = 'mwl'
): Promise<PrayerTimesData> {
  const prayers = calculatePrayerTimes(
    location.coordinates.latitude,
    location.coordinates.longitude,
    date,
    methodId
  )
  
  const nextPrayer = getNextPrayer(prayers)
  
  return {
    date: date.toISOString().split('T')[0],
    location: location.name,
    coordinates: location.coordinates,
    prayers,
    nextPrayer
  }
}

// الحصول على الموقع الحالي للمستخدم
export function getCurrentLocation(): Promise<Location> {
  return new Promise((resolve, reject) => {
    if (!navigator.geolocation) {
      reject(new Error('Geolocation is not supported'))
      return
    }
    
    navigator.geolocation.getCurrentPosition(
      (position) => {
        resolve({
          name: 'موقعي الحالي',
          country: '',
          coordinates: {
            latitude: position.coords.latitude,
            longitude: position.coords.longitude
          },
          timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
        })
      },
      (error) => {
        reject(error)
      }
    )
  })
}
