'use client'

import { useState } from 'react'
import { Settings, Bell, Palette, Type, Download, Upload, Trash2, Info } from 'lucide-react'
import { NotificationSettings } from '@/components/NotificationSettings'
import { useTheme } from '@/components/ThemeProvider'
import { useProgress } from '@/hooks/useProgress'
import { useFavorites } from '@/hooks/useFavorites'

type SettingsTab = 'general' | 'notifications' | 'appearance' | 'data'

export default function SettingsPage() {
  const [activeTab, setActiveTab] = useState<SettingsTab>('general')
  const { theme, toggleTheme } = useTheme()
  const { resetProgress } = useProgress()
  const { clearAllFavorites, favorites } = useFavorites()

  const tabs = [
    { id: 'general' as const, name: 'عام', icon: Settings },
    { id: 'notifications' as const, name: 'التنبيهات', icon: Bell },
    { id: 'appearance' as const, name: 'المظهر', icon: Pa<PERSON> },
    { id: 'data' as const, name: 'البيانات', icon: Download },
  ]

  const handleExportData = () => {
    const data = {
      favorites,
      progress: localStorage.getItem('noor-al-iman-progress'),
      settings: localStorage.getItem('notification-preferences'),
      exportDate: new Date().toISOString()
    }

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `noor-al-iman-backup-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const handleImportData = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const data = JSON.parse(e.target?.result as string)
        
        if (data.progress) {
          localStorage.setItem('noor-al-iman-progress', data.progress)
        }
        if (data.settings) {
          localStorage.setItem('notification-preferences', data.settings)
        }
        if (data.favorites) {
          localStorage.setItem('noor-al-iman-favorites', JSON.stringify(data.favorites))
        }

        alert('تم استيراد البيانات بنجاح! سيتم إعادة تحميل الصفحة.')
        window.location.reload()
      } catch (error) {
        alert('خطأ في استيراد البيانات. تأكد من صحة الملف.')
      }
    }
    reader.readAsText(file)
  }

  const handleResetAllData = () => {
    if (confirm('هل أنت متأكد من حذف جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه.')) {
      resetProgress()
      clearAllFavorites()
      localStorage.removeItem('notification-preferences')
      alert('تم حذف جميع البيانات بنجاح!')
      window.location.reload()
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center py-6">
        <h1 className="text-3xl md:text-4xl font-bold text-gray-800 dark:text-white mb-4">
          الإعدادات
        </h1>
        <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
          تخصيص تجربتك في تطبيق نور الإيمان
        </p>
      </div>

      {/* Tabs */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md">
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="flex space-x-8 space-x-reverse px-6">
            {tabs.map((tab) => {
              const Icon = tab.icon
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center space-x-2 space-x-reverse py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                      : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
                  }`}
                >
                  <Icon className="h-4 w-4" />
                  <span>{tab.name}</span>
                </button>
              )
            })}
          </nav>
        </div>

        <div className="p-6">
          {/* General Settings */}
          {activeTab === 'general' && (
            <div className="space-y-6">
              <div>
                <h2 className="text-xl font-semibold text-gray-800 dark:text-white mb-4">
                  الإعدادات العامة
                </h2>
                
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div>
                      <h3 className="font-medium text-gray-800 dark:text-white">
                        الوضع الليلي
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        تبديل بين الوضع الليلي والنهاري
                      </p>
                    </div>
                    <button
                      onClick={toggleTheme}
                      className={`px-4 py-2 rounded-lg transition-colors duration-200 ${
                        theme === 'dark'
                          ? 'bg-yellow-500 text-yellow-900 hover:bg-yellow-400'
                          : 'bg-gray-800 text-white hover:bg-gray-700'
                      }`}
                    >
                      {theme === 'dark' ? 'نهاري' : 'ليلي'}
                    </button>
                  </div>

                  <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <h3 className="font-medium text-gray-800 dark:text-white mb-2">
                      معلومات التطبيق
                    </h3>
                    <div className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                      <p><strong>الإصدار:</strong> 1.0.0</p>
                      <p><strong>تاريخ الإصدار:</strong> ديسمبر 2024</p>
                      <p><strong>المطور:</strong> فريق نور الإيمان</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Notifications */}
          {activeTab === 'notifications' && (
            <NotificationSettings />
          )}

          {/* Appearance */}
          {activeTab === 'appearance' && (
            <div className="space-y-6">
              <div>
                <h2 className="text-xl font-semibold text-gray-800 dark:text-white mb-4">
                  إعدادات المظهر
                </h2>
                
                <div className="space-y-4">
                  <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div className="flex items-center space-x-3 space-x-reverse mb-3">
                      <Palette className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                      <h3 className="font-medium text-gray-800 dark:text-white">
                        نظام الألوان
                      </h3>
                    </div>
                    <div className="grid grid-cols-3 gap-3">
                      <button className="p-3 bg-white dark:bg-gray-800 border-2 border-blue-500 rounded-lg text-center">
                        <div className="w-full h-4 bg-blue-500 rounded mb-2"></div>
                        <span className="text-xs text-gray-700 dark:text-gray-300">أزرق (افتراضي)</span>
                      </button>
                      <button className="p-3 bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 rounded-lg text-center opacity-50">
                        <div className="w-full h-4 bg-green-500 rounded mb-2"></div>
                        <span className="text-xs text-gray-700 dark:text-gray-300">أخضر</span>
                      </button>
                      <button className="p-3 bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 rounded-lg text-center opacity-50">
                        <div className="w-full h-4 bg-purple-500 rounded mb-2"></div>
                        <span className="text-xs text-gray-700 dark:text-gray-300">بنفسجي</span>
                      </button>
                    </div>
                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                      المزيد من الألوان قريباً إن شاء الله
                    </p>
                  </div>

                  <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div className="flex items-center space-x-3 space-x-reverse mb-3">
                      <Type className="h-5 w-5 text-green-600 dark:text-green-400" />
                      <h3 className="font-medium text-gray-800 dark:text-white">
                        الخطوط
                      </h3>
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-gray-700 dark:text-gray-300">خط القرآن</span>
                        <select className="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-gray-900 dark:text-white text-sm">
                          <option>أميري (افتراضي)</option>
                          <option>عثماني</option>
                          <option>نسخ</option>
                        </select>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-gray-700 dark:text-gray-300">خط الأذكار</span>
                        <select className="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-gray-900 dark:text-white text-sm">
                          <option>أميري (افتراضي)</option>
                          <option>نسخ</option>
                          <option>كوفي</option>
                        </select>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Data Management */}
          {activeTab === 'data' && (
            <div className="space-y-6">
              <div>
                <h2 className="text-xl font-semibold text-gray-800 dark:text-white mb-4">
                  إدارة البيانات
                </h2>
                
                <div className="space-y-4">
                  {/* Export Data */}
                  <div className="p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3 space-x-reverse">
                        <Download className="h-5 w-5 text-green-600 dark:text-green-400" />
                        <div>
                          <h3 className="font-medium text-green-800 dark:text-green-200">
                            تصدير البيانات
                          </h3>
                          <p className="text-sm text-green-700 dark:text-green-300">
                            احفظ نسخة احتياطية من تقدمك ومفضلتك
                          </p>
                        </div>
                      </div>
                      <button
                        onClick={handleExportData}
                        className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200"
                      >
                        تصدير
                      </button>
                    </div>
                  </div>

                  {/* Import Data */}
                  <div className="p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3 space-x-reverse">
                        <Upload className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                        <div>
                          <h3 className="font-medium text-blue-800 dark:text-blue-200">
                            استيراد البيانات
                          </h3>
                          <p className="text-sm text-blue-700 dark:text-blue-300">
                            استرجع بياناتك من نسخة احتياطية
                          </p>
                        </div>
                      </div>
                      <label className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 cursor-pointer">
                        استيراد
                        <input
                          type="file"
                          accept=".json"
                          onChange={handleImportData}
                          className="hidden"
                        />
                      </label>
                    </div>
                  </div>

                  {/* Data Statistics */}
                  <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div className="flex items-center space-x-3 space-x-reverse mb-3">
                      <Info className="h-5 w-5 text-gray-600 dark:text-gray-400" />
                      <h3 className="font-medium text-gray-800 dark:text-white">
                        إحصائيات البيانات
                      </h3>
                    </div>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div className="text-center p-2 bg-white dark:bg-gray-800 rounded">
                        <div className="text-lg font-bold text-blue-600 dark:text-blue-400">
                          {favorites.length}
                        </div>
                        <div className="text-gray-600 dark:text-gray-400">مفضلة</div>
                      </div>
                      <div className="text-center p-2 bg-white dark:bg-gray-800 rounded">
                        <div className="text-lg font-bold text-green-600 dark:text-green-400">
                          {localStorage.getItem('noor-al-iman-progress') ? '✓' : '✗'}
                        </div>
                        <div className="text-gray-600 dark:text-gray-400">تقدم محفوظ</div>
                      </div>
                    </div>
                  </div>

                  {/* Reset Data */}
                  <div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3 space-x-reverse">
                        <Trash2 className="h-5 w-5 text-red-600 dark:text-red-400" />
                        <div>
                          <h3 className="font-medium text-red-800 dark:text-red-200">
                            حذف جميع البيانات
                          </h3>
                          <p className="text-sm text-red-700 dark:text-red-300">
                            احذف جميع التقدم والمفضلة والإعدادات
                          </p>
                        </div>
                      </div>
                      <button
                        onClick={handleResetAllData}
                        className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors duration-200"
                      >
                        حذف الكل
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
