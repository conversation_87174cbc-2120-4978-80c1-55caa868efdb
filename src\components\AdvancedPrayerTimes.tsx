'use client'

import { useState, useEffect } from 'react'
import { Clock, MapPin, Bell, Settings, Navigation, RefreshCw } from 'lucide-react'
import { 
  PrayerTimesData, 
  Location, 
  popularLocations, 
  calculationMethods,
  getPrayerTimesForLocation,
  getCurrentLocation 
} from '@/services/prayerTimes'

export function AdvancedPrayerTimes() {
  const [prayerData, setPrayerData] = useState<PrayerTimesData | null>(null)
  const [selectedLocation, setSelectedLocation] = useState<Location>(popularLocations[0])
  const [calculationMethod, setCalculationMethod] = useState('mwl')
  const [isLoading, setIsLoading] = useState(true)
  const [showSettings, setShowSettings] = useState(false)
  const [notifications, setNotifications] = useState({
    fajr: true,
    dhuhr: true,
    asr: true,
    maghrib: true,
    isha: true
  })

  useEffect(() => {
    loadPrayerTimes()
  }, [selectedLocation, calculationMethod])

  const loadPrayerTimes = async () => {
    setIsLoading(true)
    try {
      const data = await getPrayerTimesForLocation(selectedLocation, new Date(), calculationMethod)
      setPrayerData(data)
    } catch (error) {
      console.error('Error loading prayer times:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleLocationChange = (location: Location) => {
    setSelectedLocation(location)
  }

  const handleUseCurrentLocation = async () => {
    try {
      setIsLoading(true)
      const currentLocation = await getCurrentLocation()
      setSelectedLocation(currentLocation)
    } catch (error) {
      alert('لا يمكن الحصول على موقعك الحالي. تأكد من السماح بالوصول للموقع.')
    }
  }

  const getCurrentPrayerIndex = () => {
    if (!prayerData) return -1
    
    const now = Date.now()
    const prayerTimes = prayerData.prayers.filter(p => p.name !== 'الشروق')
    
    for (let i = 0; i < prayerTimes.length; i++) {
      if (prayerTimes[i].timestamp > now) {
        return i
      }
    }
    return 0 // إذا انتهى اليوم، الصلاة القادمة هي الفجر
  }

  const formatTimeRemaining = (timestamp: number) => {
    const now = Date.now()
    const diff = timestamp - now
    
    if (diff <= 0) return '00:00'
    
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
    
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`
  }

  if (isLoading) {
    return (
      <div className="text-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
        <p className="mt-2 text-gray-600 dark:text-gray-400">جاري تحميل أوقات الصلاة...</p>
      </div>
    )
  }

  if (!prayerData) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600 dark:text-red-400">حدث خطأ في تحميل أوقات الصلاة</p>
        <button 
          onClick={loadPrayerTimes}
          className="mt-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
        >
          إعادة المحاولة
        </button>
      </div>
    )
  }

  const currentPrayerIndex = getCurrentPrayerIndex()
  const prayerTimes = prayerData.prayers.filter(p => p.name !== 'الشروق')

  return (
    <div className="space-y-6">
      {/* Header with Location and Settings */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3 space-x-reverse">
          <MapPin className="h-5 w-5 text-blue-600 dark:text-blue-400" />
          <div>
            <h3 className="font-semibold text-gray-800 dark:text-white">
              {selectedLocation.name}
            </h3>
            {selectedLocation.country && (
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {selectedLocation.country}
              </p>
            )}
          </div>
        </div>

        <div className="flex items-center space-x-2 space-x-reverse">
          <button
            onClick={handleUseCurrentLocation}
            className="p-2 bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400 rounded-lg hover:bg-green-200 dark:hover:bg-green-800 transition-colors duration-200"
            title="استخدام موقعي الحالي"
          >
            <Navigation className="h-4 w-4" />
          </button>
          <button
            onClick={loadPrayerTimes}
            className="p-2 bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 rounded-lg hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors duration-200"
            title="تحديث"
          >
            <RefreshCw className="h-4 w-4" />
          </button>
          <button
            onClick={() => setShowSettings(!showSettings)}
            className="p-2 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200"
          >
            <Settings className="h-4 w-4" />
          </button>
        </div>
      </div>

      {/* Next Prayer Countdown */}
      {prayerData.nextPrayer && (
        <div className="bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl p-6 text-center">
          <h3 className="text-lg font-semibold mb-2">الصلاة القادمة</h3>
          <div className="text-3xl font-bold mb-2">{prayerData.nextPrayer.name}</div>
          <div className="text-xl opacity-90">
            بعد {formatTimeRemaining(prayerData.nextPrayer.timestamp)}
          </div>
        </div>
      )}

      {/* Settings Panel */}
      {showSettings && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 space-y-4">
          <h4 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">الإعدادات</h4>
          
          {/* Location Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              اختيار المدينة
            </label>
            <select
              value={selectedLocation.name}
              onChange={(e) => {
                const location = popularLocations.find(l => l.name === e.target.value)
                if (location) handleLocationChange(location)
              }}
              className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            >
              {popularLocations.map((location) => (
                <option key={location.name} value={location.name}>
                  {location.name} - {location.country}
                </option>
              ))}
            </select>
          </div>

          {/* Calculation Method */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              طريقة الحساب
            </label>
            <select
              value={calculationMethod}
              onChange={(e) => setCalculationMethod(e.target.value)}
              className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            >
              {calculationMethods.map((method) => (
                <option key={method.id} value={method.id}>
                  {method.name}
                </option>
              ))}
            </select>
          </div>

          {/* Notification Settings */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              تفعيل التنبيهات
            </label>
            <div className="space-y-2">
              {prayerTimes.map((prayer) => (
                <div key={prayer.name} className="flex items-center justify-between">
                  <span className="text-gray-700 dark:text-gray-300">{prayer.name}</span>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={notifications[prayer.name.toLowerCase() as keyof typeof notifications] || false}
                      onChange={(e) => setNotifications(prev => ({
                        ...prev,
                        [prayer.name.toLowerCase()]: e.target.checked
                      }))}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                  </label>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Prayer Times Grid */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
        {prayerTimes.map((prayer, index) => (
          <div
            key={prayer.name}
            className={`p-4 rounded-lg text-center transition-all duration-300 ${
              index === currentPrayerIndex
                ? 'bg-blue-100 dark:bg-blue-900 border-2 border-blue-300 dark:border-blue-700 scale-105 shadow-lg'
                : 'bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 shadow-md'
            }`}
          >
            <div className="flex items-center justify-center mb-3">
              <div className={`p-2 rounded-full ${
                index === currentPrayerIndex
                  ? 'bg-blue-200 dark:bg-blue-800'
                  : 'bg-gray-200 dark:bg-gray-600'
              }`}>
                <Clock className={`h-5 w-5 ${
                  index === currentPrayerIndex 
                    ? 'text-blue-600 dark:text-blue-400' 
                    : 'text-gray-600 dark:text-gray-400'
                }`} />
              </div>
            </div>
            
            <h3 className={`font-semibold mb-2 ${
              index === currentPrayerIndex
                ? 'text-blue-800 dark:text-blue-300'
                : 'text-gray-800 dark:text-white'
            }`}>
              {prayer.arabic}
            </h3>
            
            <p className={`text-lg font-mono ${
              index === currentPrayerIndex
                ? 'text-blue-600 dark:text-blue-400'
                : 'text-gray-600 dark:text-gray-300'
            }`}>
              {prayer.time}
            </p>

            {index === currentPrayerIndex && prayerData.nextPrayer && (
              <p className="text-xs text-blue-500 dark:text-blue-400 mt-1">
                بعد {formatTimeRemaining(prayerData.nextPrayer.timestamp)}
              </p>
            )}
          </div>
        ))}
      </div>

      {/* Additional Info */}
      <div className="text-center text-sm text-gray-600 dark:text-gray-400">
        <p>
          التاريخ: {new Date().toLocaleDateString('ar-SA')} • 
          طريقة الحساب: {calculationMethods.find(m => m.id === calculationMethod)?.name}
        </p>
      </div>
    </div>
  )
}
