'use client'

import { useState } from 'react'
import { Search, BookOpen, X } from 'lucide-react'

interface SearchResult {
  surahNumber: number
  surahName: string
  ayahNumber: number
  ayahText: string
  highlightedText: string
}

interface QuranSearchProps {
  isOpen: boolean
  onClose: () => void
  onResultClick: (surahNumber: number, ayahNumber: number) => void
}

export function QuranSearch({ isOpen, onClose, onResultClick }: QuranSearchProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [searchResults, setSearchResults] = useState<SearchResult[]>([])
  const [isSearching, setIsSearching] = useState(false)

  // بيانات تجريبية للبحث
  const mockSearchResults: SearchResult[] = [
    {
      surahNumber: 2,
      surahName: 'البقرة',
      ayahNumber: 255,
      ayahText: 'اللَّهُ لَا إِلَٰهَ إِلَّا هُوَ الْحَيُّ الْقَيُّومُ ۚ لَا تَأْخُذُهُ سِنَةٌ وَلَا نَوْمٌ',
      highlightedText: 'اللَّهُ لَا إِلَٰهَ إِلَّا هُوَ الْحَيُّ الْقَيُّومُ'
    },
    {
      surahNumber: 3,
      surahName: 'آل عمران',
      ayahNumber: 2,
      ayahText: 'اللَّهُ لَا إِلَٰهَ إِلَّا هُوَ الْحَيُّ الْقَيُّومُ',
      highlightedText: 'اللَّهُ لَا إِلَٰهَ إِلَّا هُوَ الْحَيُّ الْقَيُّومُ'
    },
    {
      surahNumber: 20,
      surahName: 'طه',
      ayahNumber: 111,
      ayahText: 'وَعَنَتِ الْوُجُوهُ لِلْحَيِّ الْقَيُّومِ ۖ وَقَدْ خَابَ مَنْ حَمَلَ ظُلْمًا',
      highlightedText: 'وَعَنَتِ الْوُجُوهُ لِلْحَيِّ الْقَيُّومِ'
    }
  ]

  const handleSearch = async (term: string) => {
    if (term.length < 2) {
      setSearchResults([])
      return
    }

    setIsSearching(true)
    
    // محاكاة البحث
    setTimeout(() => {
      if (term.includes('الله') || term.includes('الحي') || term.includes('القيوم')) {
        setSearchResults(mockSearchResults)
      } else {
        setSearchResults([])
      }
      setIsSearching(false)
    }, 500)
  }

  const handleInputChange = (value: string) => {
    setSearchTerm(value)
    handleSearch(value)
  }

  const handleResultClick = (result: SearchResult) => {
    onResultClick(result.surahNumber, result.ayahNumber)
    onClose()
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-start justify-center p-4 pt-20">
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-2xl max-w-4xl w-full max-h-[80vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-3 space-x-reverse">
            <Search className="h-6 w-6 text-blue-600 dark:text-blue-400" />
            <h2 className="text-2xl font-bold text-gray-800 dark:text-white">
              البحث في القرآن الكريم
            </h2>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors duration-200"
          >
            <X className="h-5 w-5 text-gray-600 dark:text-gray-400" />
          </button>
        </div>

        {/* Search Input */}
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="relative">
            <Search className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
            <input
              type="text"
              placeholder="ابحث في القرآن الكريم... (مثال: الله، الحي القيوم)"
              value={searchTerm}
              onChange={(e) => handleInputChange(e.target.value)}
              className="w-full pr-12 pl-4 py-4 text-lg border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              autoFocus
            />
          </div>
          
          <div className="mt-3 text-sm text-gray-600 dark:text-gray-400">
            <p>نصائح للبحث:</p>
            <ul className="list-disc list-inside mt-1 space-y-1">
              <li>ابحث بكلمة واحدة أو أكثر</li>
              <li>يمكنك البحث بجزء من الآية</li>
              <li>البحث يدعم النص العربي فقط</li>
            </ul>
          </div>
        </div>

        {/* Search Results */}
        <div className="flex-1 overflow-y-auto max-h-96">
          {isSearching ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="mr-3 text-gray-600 dark:text-gray-400">جاري البحث...</span>
            </div>
          ) : searchTerm.length === 0 ? (
            <div className="text-center py-12">
              <Search className="h-16 w-16 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
              <p className="text-gray-500 dark:text-gray-400">
                ابدأ بكتابة كلمة للبحث في القرآن الكريم
              </p>
            </div>
          ) : searchResults.length === 0 ? (
            <div className="text-center py-12">
              <BookOpen className="h-16 w-16 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
              <p className="text-gray-500 dark:text-gray-400">
                لم يتم العثور على نتائج للبحث "{searchTerm}"
              </p>
              <p className="text-sm text-gray-400 dark:text-gray-500 mt-2">
                جرب كلمات أخرى أو تأكد من الإملاء
              </p>
            </div>
          ) : (
            <div className="divide-y divide-gray-200 dark:divide-gray-700">
              {searchResults.map((result, index) => (
                <div
                  key={index}
                  onClick={() => handleResultClick(result)}
                  className="p-6 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer transition-colors duration-200"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 space-x-reverse mb-3">
                        <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 rounded-full flex items-center justify-center text-sm font-semibold">
                          {result.surahNumber}
                        </div>
                        <div>
                          <h3 className="font-semibold text-gray-800 dark:text-white">
                            سورة {result.surahName}
                          </h3>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            الآية {result.ayahNumber}
                          </p>
                        </div>
                      </div>
                      
                      <div className="quran-text text-lg leading-relaxed text-gray-800 dark:text-white">
                        {result.ayahText}
                      </div>
                    </div>
                    
                    <BookOpen className="h-5 w-5 text-gray-400 mr-4 mt-1" />
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Footer */}
        {searchResults.length > 0 && (
          <div className="p-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700">
            <p className="text-sm text-gray-600 dark:text-gray-400 text-center">
              تم العثور على {searchResults.length} نتيجة للبحث "{searchTerm}"
            </p>
          </div>
        )}
      </div>
    </div>
  )
}
