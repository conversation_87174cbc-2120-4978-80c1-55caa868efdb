'use client'

import { useState, useEffect } from 'react'
import { BarChart3, TrendingUp, Calendar, Award, BookOpen, Heart, Clock, Target } from 'lucide-react'
import { useProgress } from '@/hooks/useProgress'
import { useFavorites } from '@/hooks/useFavorites'

export default function StatsPage() {
  const { getStatistics, progress } = useProgress()
  const { favorites } = useFavorites()
  const [stats, setStats] = useState<any>(null)

  useEffect(() => {
    setStats(getStatistics())
  }, [progress])

  if (!stats) {
    return (
      <div className="text-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
        <p className="mt-2 text-gray-600 dark:text-gray-400">جاري تحميل الإحصائيات...</p>
      </div>
    )
  }

  const getStreakColor = (days: number) => {
    if (days >= 30) return 'text-purple-600 dark:text-purple-400'
    if (days >= 7) return 'text-green-600 dark:text-green-400'
    if (days >= 3) return 'text-blue-600 dark:text-blue-400'
    return 'text-gray-600 dark:text-gray-400'
  }

  const getStreakIcon = (days: number) => {
    if (days >= 30) return '🏆'
    if (days >= 7) return '🔥'
    if (days >= 3) return '⭐'
    return '📈'
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center py-6">
        <h1 className="text-3xl md:text-4xl font-bold text-gray-800 dark:text-white mb-4">
          الإحصائيات والتقدم
        </h1>
        <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
          تتبع تقدمك في القراءة والأذكار والعبادات اليومية
        </p>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Azkar Streak */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <div className={`text-3xl font-bold ${getStreakColor(stats.azkar.streakDays)}`}>
              {stats.azkar.streakDays}
            </div>
            <div className="text-2xl">{getStreakIcon(stats.azkar.streakDays)}</div>
          </div>
          <h3 className="font-semibold text-gray-800 dark:text-white mb-1">
            سلسلة الأذكار
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            أيام متتالية
          </p>
        </div>

        {/* Today's Completion */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="text-3xl font-bold text-green-600 dark:text-green-400">
              {stats.azkar.completionRate}%
            </div>
            <Target className="h-8 w-8 text-green-600 dark:text-green-400" />
          </div>
          <h3 className="font-semibold text-gray-800 dark:text-white mb-1">
            إنجاز اليوم
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            {stats.azkar.completedToday} من {stats.azkar.totalCategories} فئات
          </p>
        </div>

        {/* Bookmarks */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="text-3xl font-bold text-blue-600 dark:text-blue-400">
              {stats.quran.totalBookmarks}
            </div>
            <BookOpen className="h-8 w-8 text-blue-600 dark:text-blue-400" />
          </div>
          <h3 className="font-semibold text-gray-800 dark:text-white mb-1">
            الإشارات المرجعية
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            في القرآن الكريم
          </p>
        </div>

        {/* Favorites */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="text-3xl font-bold text-red-600 dark:text-red-400">
              {favorites.length}
            </div>
            <Heart className="h-8 w-8 text-red-600 dark:text-red-400" />
          </div>
          <h3 className="font-semibold text-gray-800 dark:text-white mb-1">
            المفضلة
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            آيات وأذكار محفوظة
          </p>
        </div>
      </div>

      {/* Detailed Stats */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Quran Progress */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
          <div className="flex items-center space-x-3 space-x-reverse mb-6">
            <BookOpen className="h-6 w-6 text-green-600 dark:text-green-400" />
            <h2 className="text-xl font-bold text-gray-800 dark:text-white">
              تقدم القرآن الكريم
            </h2>
          </div>

          <div className="space-y-4">
            {stats.quran.lastRead ? (
              <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                <h3 className="font-semibold text-green-800 dark:text-green-200 mb-2">
                  آخر قراءة
                </h3>
                <p className="text-green-700 dark:text-green-300">
                  سورة رقم {stats.quran.lastRead.surahNumber} - الآية {stats.quran.lastRead.ayahNumber}
                </p>
                <p className="text-sm text-green-600 dark:text-green-400 mt-1">
                  {new Date(stats.quran.lastRead.lastRead).toLocaleDateString('ar-SA')}
                </p>
              </div>
            ) : (
              <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg text-center">
                <p className="text-gray-600 dark:text-gray-400">
                  لم تبدأ القراءة بعد
                </p>
              </div>
            )}

            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                  {stats.quran.totalBookmarks}
                </div>
                <p className="text-sm text-blue-700 dark:text-blue-300">إشارات مرجعية</p>
              </div>
              <div className="text-center p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                  {Math.round(stats.quran.totalReadingTime)}
                </div>
                <p className="text-sm text-purple-700 dark:text-purple-300">دقائق قراءة</p>
              </div>
            </div>
          </div>
        </div>

        {/* Azkar Progress */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
          <div className="flex items-center space-x-3 space-x-reverse mb-6">
            <Heart className="h-6 w-6 text-purple-600 dark:text-purple-400" />
            <h2 className="text-xl font-bold text-gray-800 dark:text-white">
              تقدم الأذكار
            </h2>
          </div>

          <div className="space-y-4">
            {/* Streak Info */}
            <div className="p-4 bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 rounded-lg">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-semibold text-gray-800 dark:text-white mb-1">
                    السلسلة الحالية
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    أيام متتالية من إكمال الأذكار
                  </p>
                </div>
                <div className="text-right">
                  <div className={`text-3xl font-bold ${getStreakColor(stats.azkar.streakDays)}`}>
                    {stats.azkar.streakDays}
                  </div>
                  <div className="text-2xl">{getStreakIcon(stats.azkar.streakDays)}</div>
                </div>
              </div>
            </div>

            {/* Today's Progress */}
            <div className="space-y-3">
              <h3 className="font-semibold text-gray-800 dark:text-white">
                تقدم اليوم
              </h3>
              <div className="flex items-center justify-between">
                <span className="text-gray-700 dark:text-gray-300">الإكمال</span>
                <span className="font-semibold text-green-600 dark:text-green-400">
                  {stats.azkar.completionRate}%
                </span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div 
                  className="bg-green-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${stats.azkar.completionRate}%` }}
                ></div>
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {stats.azkar.completedToday} من {stats.azkar.totalCategories} فئات مكتملة
              </p>
            </div>

            {/* Achievements */}
            <div className="space-y-2">
              <h3 className="font-semibold text-gray-800 dark:text-white">
                الإنجازات
              </h3>
              <div className="grid grid-cols-2 gap-2">
                <div className={`p-2 rounded-lg text-center ${
                  stats.azkar.streakDays >= 3 
                    ? 'bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-200' 
                    : 'bg-gray-100 dark:bg-gray-700 text-gray-500 dark:text-gray-400'
                }`}>
                  <div className="text-lg">⭐</div>
                  <p className="text-xs">3 أيام</p>
                </div>
                <div className={`p-2 rounded-lg text-center ${
                  stats.azkar.streakDays >= 7 
                    ? 'bg-orange-100 dark:bg-orange-900/20 text-orange-800 dark:text-orange-200' 
                    : 'bg-gray-100 dark:bg-gray-700 text-gray-500 dark:text-gray-400'
                }`}>
                  <div className="text-lg">🔥</div>
                  <p className="text-xs">أسبوع</p>
                </div>
                <div className={`p-2 rounded-lg text-center ${
                  stats.azkar.streakDays >= 30 
                    ? 'bg-purple-100 dark:bg-purple-900/20 text-purple-800 dark:text-purple-200' 
                    : 'bg-gray-100 dark:bg-gray-700 text-gray-500 dark:text-gray-400'
                }`}>
                  <div className="text-lg">🏆</div>
                  <p className="text-xs">شهر</p>
                </div>
                <div className={`p-2 rounded-lg text-center ${
                  favorites.length >= 10 
                    ? 'bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-200' 
                    : 'bg-gray-100 dark:bg-gray-700 text-gray-500 dark:text-gray-400'
                }`}>
                  <div className="text-lg">❤️</div>
                  <p className="text-xs">جامع</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Weekly Chart Placeholder */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
        <div className="flex items-center space-x-3 space-x-reverse mb-6">
          <BarChart3 className="h-6 w-6 text-blue-600 dark:text-blue-400" />
          <h2 className="text-xl font-bold text-gray-800 dark:text-white">
            النشاط الأسبوعي
          </h2>
        </div>
        
        <div className="text-center py-8">
          <TrendingUp className="h-16 w-16 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
          <p className="text-gray-500 dark:text-gray-400">
            سيتم إضافة الرسوم البيانية قريباً إن شاء الله
          </p>
        </div>
      </div>

      {/* Goals Section */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
        <div className="flex items-center space-x-3 space-x-reverse mb-6">
          <Target className="h-6 w-6 text-green-600 dark:text-green-400" />
          <h2 className="text-xl font-bold text-gray-800 dark:text-white">
            الأهداف المقترحة
          </h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div className="p-4 border-2 border-dashed border-blue-300 dark:border-blue-700 rounded-lg text-center">
            <div className="text-2xl mb-2">📖</div>
            <h3 className="font-semibold text-gray-800 dark:text-white mb-1">
              قراءة يومية
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              اقرأ صفحة واحدة من القرآن يومياً
            </p>
          </div>

          <div className="p-4 border-2 border-dashed border-green-300 dark:border-green-700 rounded-lg text-center">
            <div className="text-2xl mb-2">🤲</div>
            <h3 className="font-semibold text-gray-800 dark:text-white mb-1">
              أذكار منتظمة
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              أكمل أذكار الصباح والمساء يومياً
            </p>
          </div>

          <div className="p-4 border-2 border-dashed border-purple-300 dark:border-purple-700 rounded-lg text-center">
            <div className="text-2xl mb-2">⭐</div>
            <h3 className="font-semibold text-gray-800 dark:text-white mb-1">
              سلسلة أسبوعية
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              حافظ على سلسلة 7 أيام متتالية
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
