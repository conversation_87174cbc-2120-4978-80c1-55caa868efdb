'use client'

import { useState, useEffect } from 'react'
import { <PERSON>R<PERSON>, BookOpen, Volume2, Heart, Share2, Setting<PERSON>, Play, Pause } from 'lucide-react'
import { Surah, Ayah, getSurahByNumber, getAyahsBySurah } from '@/data/quran'
import { QuranSettings } from './QuranSettings'

interface SurahReaderProps {
  surahNumber: number
  onBack: () => void
}

export function SurahReader({ surahNumber, onBack }: SurahReaderProps) {
  const [surah, setSurah] = useState<Surah | null>(null)
  const [ayahs, setAyahs] = useState<Ayah[]>([])
  const [fontSize, setFontSize] = useState(24)
  const [fontFamily, setFontFamily] = useState('Amiri')
  const [readingTheme, setReadingTheme] = useState('light')
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentAyah, setCurrentAyah] = useState<number | null>(null)
  const [showTranslation, setShowTranslation] = useState(false)
  const [showTafsir, setShowTafsir] = useState(false)
  const [showSettings, setShowSettings] = useState(false)

  useEffect(() => {
    const surahData = getSurahByNumber(surahNumber)
    const ayahsData = getAyahsBySurah(surahNumber)
    
    setSurah(surahData || null)
    setAyahs(ayahsData)
  }, [surahNumber])

  const handlePlayPause = () => {
    setIsPlaying(!isPlaying)
    // في التطبيق الحقيقي سنضيف تشغيل الصوت
  }

  const handleAyahClick = (ayahNumber: number) => {
    setCurrentAyah(currentAyah === ayahNumber ? null : ayahNumber)
  }

  const handleShare = (ayah: Ayah) => {
    // في التطبيق الحقيقي سنضيف مشاركة الآية
    alert(`مشاركة الآية ${ayah.numberInSurah} من سورة ${surah?.name}`)
  }

  const handleBookmark = (ayah: Ayah) => {
    // في التطبيق الحقيقي سنضيف إلى المفضلة
    alert(`تم إضافة الآية ${ayah.numberInSurah} من سورة ${surah?.name} إلى المفضلة`)
  }

  if (!surah) {
    return (
      <div className="text-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
        <p className="mt-2 text-gray-600 dark:text-gray-400">جاري التحميل...</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <div className="flex items-center justify-between mb-4">
          <button
            onClick={onBack}
            className="flex items-center space-x-2 space-x-reverse text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300"
          >
            <ArrowRight className="h-5 w-5" />
            <span>العودة للفهرس</span>
          </button>

          <div className="flex items-center space-x-2 space-x-reverse">
            <button
              onClick={handlePlayPause}
              className="p-2 bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400 rounded-lg hover:bg-green-200 dark:hover:bg-green-800"
            >
              {isPlaying ? <Pause className="h-5 w-5" /> : <Play className="h-5 w-5" />}
            </button>
            <button
              onClick={() => setShowSettings(true)}
              className="p-2 bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 rounded-lg hover:bg-blue-200 dark:hover:bg-blue-800"
            >
              <Settings className="h-5 w-5" />
            </button>
          </div>
        </div>

        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-800 dark:text-white mb-2">
            سورة {surah.name}
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            {surah.englishName} • {surah.numberOfAyahs} آية • {surah.revelationTypeArabic}
          </p>

          {/* Bismillah for non-Tawbah surahs */}
          {surah.number !== 1 && surah.number !== 9 && (
            <div className="quran-text text-2xl text-gray-800 dark:text-white mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
              بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ
            </div>
          )}
        </div>
      </div>

      {/* Reading Controls */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
        <div className="flex flex-wrap items-center justify-between gap-4">
          <div className="flex items-center space-x-4 space-x-reverse">
            <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
              حجم الخط:
            </label>
            <input
              type="range"
              min="16"
              max="36"
              value={fontSize}
              onChange={(e) => setFontSize(Number(e.target.value))}
              className="w-24"
            />
            <span className="text-sm text-gray-600 dark:text-gray-400">{fontSize}px</span>
          </div>

          <div className="flex items-center space-x-4 space-x-reverse">
            <label className="flex items-center space-x-2 space-x-reverse cursor-pointer">
              <input
                type="checkbox"
                checked={showTranslation}
                onChange={(e) => setShowTranslation(e.target.checked)}
                className="rounded"
              />
              <span className="text-sm text-gray-700 dark:text-gray-300">إظهار الترجمة</span>
            </label>

            <label className="flex items-center space-x-2 space-x-reverse cursor-pointer">
              <input
                type="checkbox"
                checked={showTafsir}
                onChange={(e) => setShowTafsir(e.target.checked)}
                className="rounded"
              />
              <span className="text-sm text-gray-700 dark:text-gray-300">إظهار التفسير</span>
            </label>
          </div>
        </div>
      </div>

      {/* Ayahs */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md">
        {ayahs.length === 0 ? (
          <div className="p-8 text-center">
            <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600 dark:text-gray-400">
              لم يتم تحميل آيات هذه السورة بعد
            </p>
            <p className="text-sm text-gray-500 dark:text-gray-500 mt-2">
              سيتم إضافة جميع السور قريباً إن شاء الله
            </p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {ayahs.map((ayah) => (
              <div
                key={ayah.number}
                className={`p-6 transition-colors duration-200 ${
                  currentAyah === ayah.numberInSurah
                    ? 'bg-blue-50 dark:bg-blue-900/20'
                    : 'hover:bg-gray-50 dark:hover:bg-gray-700'
                }`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div
                      className="quran-text leading-relaxed text-gray-800 dark:text-white cursor-pointer"
                      style={{ fontSize: `${fontSize}px`, fontFamily: fontFamily }}
                      onClick={() => handleAyahClick(ayah.numberInSurah)}
                    >
                      {ayah.text}
                      <span className="inline-flex items-center justify-center w-8 h-8 bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 rounded-full text-sm font-semibold mr-2">
                        {ayah.numberInSurah}
                      </span>
                    </div>

                    {showTranslation && (
                      <div className="mt-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <p className="text-gray-700 dark:text-gray-300 text-sm">
                          <strong>الترجمة:</strong> سيتم إضافة الترجمة قريباً إن شاء الله
                        </p>
                      </div>
                    )}

                    {showTafsir && (
                      <div className="mt-3 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                        <p className="text-gray-700 dark:text-gray-300 text-sm">
                          <strong>التفسير:</strong> سيتم إضافة التفسير قريباً إن شاء الله
                        </p>
                      </div>
                    )}

                    {currentAyah === ayah.numberInSurah && (
                      <div className="mt-4 flex items-center space-x-2 space-x-reverse">
                        <button
                          onClick={() => handleShare(ayah)}
                          className="flex items-center space-x-1 space-x-reverse px-3 py-1 bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 rounded-md hover:bg-blue-200 dark:hover:bg-blue-800 text-sm"
                        >
                          <Share2 className="h-3 w-3" />
                          <span>مشاركة</span>
                        </button>
                        <button
                          onClick={() => handleBookmark(ayah)}
                          className="flex items-center space-x-1 space-x-reverse px-3 py-1 bg-red-100 dark:bg-red-900 text-red-600 dark:text-red-400 rounded-md hover:bg-red-200 dark:hover:bg-red-800 text-sm"
                        >
                          <Heart className="h-3 w-3" />
                          <span>مفضلة</span>
                        </button>
                        <button className="flex items-center space-x-1 space-x-reverse px-3 py-1 bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400 rounded-md hover:bg-green-200 dark:hover:bg-green-800 text-sm">
                          <Volume2 className="h-3 w-3" />
                          <span>استمع</span>
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Navigation */}
      {ayahs.length > 0 && (
        <div className="flex justify-between items-center bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
          <button
            disabled={surah.number === 1}
            className="flex items-center space-x-2 space-x-reverse px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <ArrowRight className="h-4 w-4" />
            <span>السورة السابقة</span>
          </button>

          <div className="text-center">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              الصفحة {ayahs[0]?.page || 1} • الجزء {ayahs[0]?.juz || 1}
            </p>
          </div>

          <button
            disabled={surah.number === 114}
            className="flex items-center space-x-2 space-x-reverse px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <span>السورة التالية</span>
            <ArrowRight className="h-4 w-4 rotate-180" />
          </button>
        </div>
      )}

      {/* Settings Modal */}
      <QuranSettings
        isOpen={showSettings}
        onClose={() => setShowSettings(false)}
        fontSize={fontSize}
        onFontSizeChange={setFontSize}
        fontFamily={fontFamily}
        onFontFamilyChange={setFontFamily}
        theme={readingTheme}
        onThemeChange={setReadingTheme}
      />
    </div>
  )
}
