# نور الإيمان - تطبيق إسلامي متكامل

## 🌟 نظرة عامة

**نور الإيمان** هو تطبيق إسلامي شامل ومتكامل يهدف إلى خدمة المسلمين في جميع أنحاء العالم. يجمع التطبيق بين القرآن الكريم وأوقات الصلاة والأذكار في واجهة عصرية وسهلة الاستخدام.

## ✨ المميزات الرئيسية

### 📖 القرآن الكريم
- عرض القرآن الكريم كاملاً بخط عثماني جميل
- خيارات متعددة للخط والتنسيق
- تفسير متعدد (السعدي، ابن كثير، الجلالين)
- ترجمة بعدة لغات
- ميزة القراءة الليلية
- حفظ مكان القراءة تلقائياً
- البحث الذكي في القرآن
- إمكانية تحديد ورد يومي

### 🕐 أوقات الصلاة
- حساب أوقات الصلاة بدقة حسب الموقع الجغرافي
- تنبيهات بالأذان
- عداد تنازلي للصلاة القادمة
- دعم جميع المدن العربية والعالمية
- اتجاه القبلة
- تقويم أسبوعي للمواقيت

### 🤲 الأذكار اليومية
- أذكار الصباح والمساء
- أذكار النوم والاستيقاظ
- أذكار متنوعة لكل المناسبات
- إمكانية التشغيل الصوتي
- ميزة التذكير التلقائي
- عداد للتكرار
- تتبع التقدم اليومي

### 🎨 واجهة المستخدم
- تصميم أنيق وعصري
- دعم الوضع الليلي والنهاري
- ثيمات ألوان متعددة
- حجم الخط قابل للتعديل
- واجهة متجاوبة لجميع الأجهزة
- دعم اللغة العربية بالكامل

### ⭐ المميزات المتقدمة
- نظام المفضلة للآيات والأذكار
- تتبع التقدم والإحصائيات
- حفظ آخر قراءة تلقائياً
- سلسلة الأذكار اليومية
- البحث المتقدم في القرآن
- إعدادات شخصية متقدمة

## 🛠️ التقنيات المستخدمة

- **Frontend**: Next.js 14 مع TypeScript
- **Styling**: Tailwind CSS
- **Icons**: Lucide React
- **Fonts**: خط أميري للنصوص العربية
- **Theme**: دعم الوضع الليلي/النهاري
- **Responsive**: تصميم متجاوب لجميع الأجهزة

## 🚀 التثبيت والتشغيل

### المتطلبات
- Node.js (الإصدار 18 أو أحدث)
- npm أو yarn أو pnpm

### خطوات التثبيت

1. **استنساخ المشروع**
```bash
git clone https://github.com/your-username/noor-al-iman.git
cd noor-al-iman
```

2. **تثبيت التبعيات**
```bash
npm install
# أو
yarn install
# أو
pnpm install
```

3. **تشغيل التطبيق في وضع التطوير**
```bash
npm run dev
# أو
yarn dev
# أو
pnpm dev
```

4. **فتح التطبيق**
افتح [http://localhost:3000](http://localhost:3000) في المتصفح

## 📁 هيكل المشروع

```
noor-al-iman/
├── src/
│   ├── app/                    # صفحات التطبيق (App Router)
│   │   ├── globals.css         # الأنماط العامة
│   │   ├── layout.tsx          # التخطيط الرئيسي
│   │   ├── page.tsx            # الصفحة الرئيسية
│   │   ├── quran/              # صفحة القرآن الكريم
│   │   ├── prayer-times/       # صفحة أوقات الصلاة
│   │   └── azkar/              # صفحة الأذكار
│   └── components/             # المكونات المشتركة
│       ├── Navigation.tsx      # شريط التنقل
│       ├── ThemeProvider.tsx   # مزود الثيم
│       ├── PrayerTimes.tsx     # مكون أوقات الصلاة
│       └── QuranVerse.tsx      # مكون آية اليوم
├── public/                     # الملفات العامة
├── tailwind.config.js          # إعدادات Tailwind
├── next.config.js              # إعدادات Next.js
└── package.json                # تبعيات المشروع
```

## 🎯 الخطط المستقبلية

### المرحلة الثانية
- [ ] إضافة API حقيقي لأوقات الصلاة
- [ ] تكامل مع خدمات الموقع الجغرافي
- [ ] إضافة تشغيل صوتي للقرآن والأذكار
- [ ] نظام المفضلة والإشارات المرجعية

### المرحلة الثالثة
- [ ] تطبيق موبايل باستخدام React Native
- [ ] إشعارات push للصلاة والأذكار
- [ ] مشاركة الآيات والأذكار
- [ ] إحصائيات وتقارير شخصية

### المرحلة الرابعة
- [ ] دعم لغات متعددة
- [ ] مجتمع المستخدمين
- [ ] مسابقات قرآنية
- [ ] دروس ومحاضرات إسلامية

## 🤝 المساهمة

نرحب بمساهماتكم في تطوير هذا المشروع الخيري. يمكنكم المساهمة من خلال:

1. **الإبلاغ عن الأخطاء**: افتحوا issue جديد
2. **اقتراح مميزات**: شاركوا أفكاركم معنا
3. **تطوير الكود**: أرسلوا Pull Request
4. **الترجمة**: ساعدوا في ترجمة التطبيق

### خطوات المساهمة
1. Fork المشروع
2. إنشاء branch جديد (`git checkout -b feature/amazing-feature`)
3. Commit التغييرات (`git commit -m 'Add amazing feature'`)
4. Push إلى Branch (`git push origin feature/amazing-feature`)
5. فتح Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 التواصل

- **البريد الإلكتروني**: <EMAIL>
- **الموقع الإلكتروني**: https://noor-al-iman.com
- **تويتر**: [@noor_al_iman](https://twitter.com/noor_al_iman)

## 🙏 شكر وتقدير

- شكر خاص لجميع المساهمين في هذا المشروع
- شكر لمطوري المكتبات والأدوات المستخدمة
- شكر لكل من يدعم هذا المشروع الخيري

---

**"وَمَن يَعْمَلْ مِثْقَالَ ذَرَّةٍ خَيْرًا يَرَهُ"** - الزلزلة: 7

جعل الله هذا العمل في ميزان حسناتنا وحسناتكم 🤲
