'use client'

import { useState } from 'react'
import { Heart, Volume2, <PERSON>otateC<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON> as MoonIcon, Search } from 'lucide-react'
import { azkarCategories, getAzkarCategory } from '@/data/azkar'
import { AzkarReader } from '@/components/AzkarReader'

export default function AzkarPage() {
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')

  if (selectedCategory) {
    const category = getAzkarCategory(selectedCategory)
    if (category) {
      return (
        <AzkarReader
          category={category}
          onBack={() => setSelectedCategory(null)}
        />
      )
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center py-6">
        <h1 className="text-3xl md:text-4xl font-bold text-gray-800 dark:text-white mb-4">
          الأذكار
        </h1>
        <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
          أذكار الصباح والمساء وأذكار متنوعة لحفظ المسلم وتقوية إيمانه
        </p>
      </div>

      {/* Search Bar */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <div className="relative">
          <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
          <input
            type="text"
            placeholder="ابحث في الأذكار..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pr-10 pl-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          />
        </div>
      </div>

      {/* Quick Access */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {azkarCategories.map((category) => {
          const getIcon = () => {
            switch (category.icon) {
              case 'sun': return <Sun className="h-8 w-8 mx-auto mb-2" />
              case 'moon': return <MoonIcon className="h-8 w-8 mx-auto mb-2" />
              case 'heart': return <Heart className="h-8 w-8 mx-auto mb-2" />
              default: return <RotateCcw className="h-8 w-8 mx-auto mb-2" />
            }
          }

          const getGradient = () => {
            switch (category.color) {
              case 'yellow': return 'from-yellow-400 to-orange-500'
              case 'blue': return 'from-blue-500 to-purple-600'
              case 'purple': return 'from-purple-500 to-indigo-600'
              case 'green': return 'from-green-500 to-teal-600'
              default: return 'from-gray-500 to-gray-600'
            }
          }

          return (
            <div
              key={category.id}
              onClick={() => setSelectedCategory(category.id)}
              className={`bg-gradient-to-r ${getGradient()} text-white p-6 rounded-lg shadow-md text-center cursor-pointer hover:scale-105 transform transition-all duration-200`}
            >
              {getIcon()}
              <h3 className="font-semibold mb-1">{category.name}</h3>
              <p className="text-sm opacity-90">{category.time || category.description}</p>
              <p className="text-xs opacity-75 mt-2">{category.azkar.length} ذكر</p>
            </div>
          )
        })}
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 text-center">
          <div className="text-3xl font-bold text-blue-600 dark:text-blue-400 mb-2">
            {azkarCategories.reduce((total, cat) => total + cat.azkar.length, 0)}
          </div>
          <p className="text-gray-600 dark:text-gray-400">إجمالي الأذكار</p>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 text-center">
          <div className="text-3xl font-bold text-green-600 dark:text-green-400 mb-2">
            {azkarCategories.length}
          </div>
          <p className="text-gray-600 dark:text-gray-400">فئات الأذكار</p>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 text-center">
          <div className="text-3xl font-bold text-purple-600 dark:text-purple-400 mb-2">
            24/7
          </div>
          <p className="text-gray-600 dark:text-gray-400">متاح دائماً</p>
        </div>
      </div>

      {/* Tips and Benefits */}
      <div className="bg-gradient-to-r from-green-50 to-blue-50 dark:from-gray-800 dark:to-gray-700 rounded-xl shadow-lg p-6">
        <h3 className="text-xl font-bold text-gray-800 dark:text-white mb-4 text-center">
          فوائد الأذكار
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-3">
            <h4 className="font-semibold text-gray-800 dark:text-white">الفوائد الروحية:</h4>
            <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
              <li>• تقوية الصلة بالله عز وجل</li>
              <li>• طمأنينة القلب وراحة النفس</li>
              <li>• الحماية من الشياطين والجن</li>
              <li>• مضاعفة الأجر والثواب</li>
            </ul>
          </div>
          <div className="space-y-3">
            <h4 className="font-semibold text-gray-800 dark:text-white">الفوائد الدنيوية:</h4>
            <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
              <li>• تخفيف التوتر والقلق</li>
              <li>• تحسين التركيز والذاكرة</li>
              <li>• البركة في الوقت والرزق</li>
              <li>• الحماية من المصائب</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
