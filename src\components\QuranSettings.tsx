'use client'

import { useState } from 'react'
import { Settings, X, Type, Palette, Volume2, BookOpen } from 'lucide-react'

interface QuranSettingsProps {
  isOpen: boolean
  onClose: () => void
  fontSize: number
  onFontSizeChange: (size: number) => void
  fontFamily: string
  onFontFamilyChange: (family: string) => void
  theme: string
  onThemeChange: (theme: string) => void
}

export function QuranSettings({
  isOpen,
  onClose,
  fontSize,
  onFontSizeChange,
  fontFamily,
  onFontFamilyChange,
  theme,
  onThemeChange
}: QuranSettingsProps) {
  const fontFamilies = [
    { name: 'أميري', value: 'Amiri', preview: 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ' },
    { name: 'عثماني', value: 'Uthmanic', preview: 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ' },
    { name: 'نسخ', value: 'Naskh', preview: 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ' },
    { name: 'كوفي', value: '<PERSON><PERSON>', preview: 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ' }
  ]

  const themes = [
    { name: 'فاتح', value: 'light', bg: 'bg-white', text: 'text-gray-900' },
    { name: 'داكن', value: 'dark', bg: 'bg-gray-900', text: 'text-white' },
    { name: 'بيج', value: 'sepia', bg: 'bg-yellow-50', text: 'text-yellow-900' },
    { name: 'أخضر', value: 'green', bg: 'bg-green-50', text: 'text-green-900' }
  ]

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-3 space-x-reverse">
            <Settings className="h-6 w-6 text-blue-600 dark:text-blue-400" />
            <h2 className="text-2xl font-bold text-gray-800 dark:text-white">
              إعدادات القراءة
            </h2>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors duration-200"
          >
            <X className="h-5 w-5 text-gray-600 dark:text-gray-400" />
          </button>
        </div>

        <div className="p-6 space-y-8">
          {/* Font Size */}
          <div>
            <div className="flex items-center space-x-3 space-x-reverse mb-4">
              <Type className="h-5 w-5 text-gray-600 dark:text-gray-400" />
              <h3 className="text-lg font-semibold text-gray-800 dark:text-white">
                حجم الخط
              </h3>
            </div>
            
            <div className="space-y-4">
              <div className="flex items-center space-x-4 space-x-reverse">
                <span className="text-sm text-gray-600 dark:text-gray-400 w-12">صغير</span>
                <input
                  type="range"
                  min="16"
                  max="48"
                  value={fontSize}
                  onChange={(e) => onFontSizeChange(Number(e.target.value))}
                  className="flex-1"
                />
                <span className="text-sm text-gray-600 dark:text-gray-400 w-12">كبير</span>
                <span className="text-sm font-medium text-gray-800 dark:text-white w-12">
                  {fontSize}px
                </span>
              </div>
              
              <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <p 
                  className="quran-text text-center"
                  style={{ fontSize: `${fontSize}px`, fontFamily: fontFamily }}
                >
                  بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ
                </p>
              </div>
            </div>
          </div>

          {/* Font Family */}
          <div>
            <div className="flex items-center space-x-3 space-x-reverse mb-4">
              <BookOpen className="h-5 w-5 text-gray-600 dark:text-gray-400" />
              <h3 className="text-lg font-semibold text-gray-800 dark:text-white">
                نوع الخط
              </h3>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {fontFamilies.map((font) => (
                <button
                  key={font.value}
                  onClick={() => onFontFamilyChange(font.value)}
                  className={`p-4 rounded-lg border-2 transition-all duration-200 ${
                    fontFamily === font.value
                      ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                      : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
                  }`}
                >
                  <div className="text-center">
                    <h4 className="font-semibold text-gray-800 dark:text-white mb-2">
                      {font.name}
                    </h4>
                    <p 
                      className="text-lg"
                      style={{ fontFamily: font.value }}
                    >
                      {font.preview}
                    </p>
                  </div>
                </button>
              ))}
            </div>
          </div>

          {/* Theme */}
          <div>
            <div className="flex items-center space-x-3 space-x-reverse mb-4">
              <Palette className="h-5 w-5 text-gray-600 dark:text-gray-400" />
              <h3 className="text-lg font-semibold text-gray-800 dark:text-white">
                لون الخلفية
              </h3>
            </div>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              {themes.map((themeOption) => (
                <button
                  key={themeOption.value}
                  onClick={() => onThemeChange(themeOption.value)}
                  className={`p-4 rounded-lg border-2 transition-all duration-200 ${
                    theme === themeOption.value
                      ? 'border-blue-500'
                      : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
                  }`}
                >
                  <div className="text-center">
                    <div className={`w-full h-12 rounded-md mb-2 ${themeOption.bg} border`}></div>
                    <span className="text-sm font-medium text-gray-800 dark:text-white">
                      {themeOption.name}
                    </span>
                  </div>
                </button>
              ))}
            </div>
          </div>

          {/* Audio Settings */}
          <div>
            <div className="flex items-center space-x-3 space-x-reverse mb-4">
              <Volume2 className="h-5 w-5 text-gray-600 dark:text-gray-400" />
              <h3 className="text-lg font-semibold text-gray-800 dark:text-white">
                إعدادات الصوت
              </h3>
            </div>
            
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-gray-700 dark:text-gray-300">تشغيل تلقائي</span>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input type="checkbox" className="sr-only peer" />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                </label>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  القارئ المفضل
                </label>
                <select className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                  <option>عبد الباسط عبد الصمد</option>
                  <option>محمد صديق المنشاوي</option>
                  <option>عبد الرحمن السديس</option>
                  <option>ماهر المعيقلي</option>
                  <option>مشاري راشد العفاسي</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  سرعة التشغيل
                </label>
                <input
                  type="range"
                  min="0.5"
                  max="2"
                  step="0.1"
                  defaultValue="1"
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1">
                  <span>بطيء</span>
                  <span>عادي</span>
                  <span>سريع</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end space-x-3 space-x-reverse p-6 border-t border-gray-200 dark:border-gray-700">
          <button
            onClick={onClose}
            className="px-6 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors duration-200"
          >
            إلغاء
          </button>
          <button
            onClick={onClose}
            className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200"
          >
            حفظ الإعدادات
          </button>
        </div>
      </div>
    </div>
  )
}
