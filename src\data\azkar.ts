export interface Zikr {
  id: number
  text: string
  count: number
  benefit: string
  source: string
  category: string
}

export interface AzkarCategory {
  id: string
  name: string
  description: string
  icon: string
  color: string
  time?: string
  azkar: Zikr[]
}

// أذكار الصباح
export const morningAzkar: Zikr[] = [
  {
    id: 1,
    text: "أَعُوذُ بِاللهِ مِنْ الشَّيْطَانِ الرَّجِيمِ اللّهُ لاَ إِلَـهَ إِلاَّ هُوَ الْحَيُّ الْقَيُّومُ لاَ تَأْخُذُهُ سِنَةٌ وَلاَ نَوْمٌ لَّهُ مَا فِي السَّمَاوَاتِ وَمَا فِي الأَرْضِ مَن ذَا الَّذِي يَشْفَعُ عِنْدَهُ إِلاَّ بِإِذْنِهِ يَعْلَمُ مَا بَيْنَ أَيْدِيهِمْ وَمَا خَلْفَهُمْ وَلاَ يُحِيطُونَ بِشَيْءٍ مِّنْ عِلْمِهِ إِلاَّ بِمَا شَاء وَسِعَ كُرْسِيُّهُ السَّمَاوَاتِ وَالأَرْضَ وَلاَ يَؤُودُهُ حِفْظُهُمَا وَهُوَ الْعَلِيُّ الْعَظِيمُ",
    count: 1,
    benefit: "من قرأها في بداية النهار أجير من الجن حتى يمسي",
    source: "سورة البقرة: 255",
    category: "morning"
  },
  {
    id: 2,
    text: "بِسْمِ اللهِ الَّذِي لاَ يَضُرُّ مَعَ اسْمِهِ شَيْءٌ فِي الأَرْضِ وَلاَ فِي السَّمَاء وَهُوَ السَّمِيعُ الْعَلِيمُ",
    count: 3,
    benefit: "من قالها ثلاثاً لم تصبه فجأة بلاء حتى يمسي",
    source: "أبو داود والترمذي",
    category: "morning"
  },
  {
    id: 3,
    text: "رَضِيتُ بِاللهِ رَبَّاً وَبِالإِسْلاَمِ دِيناً وَبِمُحَمَّدٍ صَلَّى اللهُ عَلَيْهِ وَسَلَّمَ رَسُولاً",
    count: 3,
    benefit: "حق على الله أن يرضيه يوم القيامة",
    source: "أبو داود والترمذي",
    category: "morning"
  },
  {
    id: 4,
    text: "اللَّهُمَّ بِكَ أَصْبَحْنَا وَبِكَ أَمْسَيْنَا وَبِكَ نَحْيَا وَبِكَ نَمُوتُ وَإِلَيْكَ النُّشُورُ",
    count: 1,
    benefit: "إقرار بالتوحيد والتوكل على الله",
    source: "الترمذي",
    category: "morning"
  },
  {
    id: 5,
    text: "اللَّهُمَّ أَنْتَ رَبِّي لا إِلَـهَ إِلاَّ أَنْتَ خَلَقْتَنِي وَأَنَا عَبْدُكَ وَأَنَا عَلَى عَهْدِكَ وَوَعْدِكَ مَا اسْتَطَعْتُ أَعُوذُ بِكَ مِنْ شَرِّ مَا صَنَعْتُ أَبُوءُ لَكَ بِنِعْمَتِكَ عَلَيَّ وَأَبُوءُ لَكَ بِذَنْبِي فَاغْفِرْ لِي فَإِنَّهُ لاَ يَغْفِرُ الذُّنُوبَ إِلاَّ أَنْتَ",
    count: 1,
    benefit: "سيد الاستغفار، من قالها موقناً بها فمات من يومه دخل الجنة",
    source: "البخاري",
    category: "morning"
  }
]

// أذكار المساء
export const eveningAzkar: Zikr[] = [
  {
    id: 1,
    text: "أَمْسَيْنَا وَأَمْسَى الْمُلْكُ لِلهِ وَالْحَمْدُ لِلهِ لاَ إِلَهَ إِلاَّ اللهُ وَحْدَهُ لاَ شَرِيكَ لَهُ لَهُ الْمُلْكُ وَلَهُ الْحَمْدُ وَهُوَ عَلَى كُلِّ شَيْءٍ قَدِيرٌ رَبِّ أَسْأَلُكَ خَيْرَ مَا فِي هَذِهِ اللَّيْلَةِ وَخَيْرَ مَا بَعْدَهَا وَأَعُوذُ بِكَ مِنْ شَرِّ مَا فِي هَذِهِ اللَّيْلَةِ وَشَرِّ مَا بَعْدَهَا",
    count: 1,
    benefit: "دعاء شامل للحماية والخير",
    source: "مسلم",
    category: "evening"
  },
  {
    id: 2,
    text: "اللَّهُمَّ بِكَ أَمْسَيْنَا وَبِكَ أَصْبَحْنَا وَبِكَ نَحْيَا وَبِكَ نَمُوتُ وَإِلَيْكَ الْمَصِيرُ",
    count: 1,
    benefit: "إقرار بالتوحيد والتوكل على الله",
    source: "الترمذي",
    category: "evening"
  },
  {
    id: 3,
    text: "أَمْسَيْنَا عَلَى فِطْرَةِ الإِسْلاَمِ وَعَلَى كَلِمَةِ الإِخْلاَصِ وَعَلَى دِينِ نَبِيِّنَا مُحَمَّدٍ صَلَّى اللهُ عَلَيْهِ وَسَلَّمَ وَعَلَى مِلَّةِ أَبِينَا إِبْرَاهِيمَ حَنِيفاً مُسْلِماً وَمَا كَانَ مِنَ الْمُشْرِكِينَ",
    count: 1,
    benefit: "تجديد العهد مع الله على الإسلام",
    source: "أحمد",
    category: "evening"
  }
]

// أذكار النوم
export const sleepAzkar: Zikr[] = [
  {
    id: 1,
    text: "بِاسْمِكَ رَبِّي وَضَعْتُ جَنْبِي وَبِكَ أَرْفَعُهُ فَإِن أَمْسَكْتَ نَفْسِي فَارْحَمْهَا وَإِنْ أَرْسَلْتَهَا فَاحْفَظْهَا بِمَا تَحْفَظُ بِهِ عِبَادَكَ الصَّالِحِينَ",
    count: 1,
    benefit: "دعاء عند النوم للحفظ والرحمة",
    source: "البخاري ومسلم",
    category: "sleep"
  },
  {
    id: 2,
    text: "اللَّهُمَّ قِنِي عَذَابَكَ يَوْمَ تَبْعَثُ عِبَادَكَ",
    count: 3,
    benefit: "الاستعاذة من عذاب الله",
    source: "أبو داود والترمذي",
    category: "sleep"
  },
  {
    id: 3,
    text: "سُبْحَانَ اللهِ وَالْحَمْدُ لِلهِ وَاللهُ أَكْبَرُ",
    count: 33,
    benefit: "التسبيح والتحميد والتكبير قبل النوم",
    source: "البخاري ومسلم",
    category: "sleep"
  }
]

// أذكار متنوعة
export const generalAzkar: Zikr[] = [
  {
    id: 1,
    text: "لاَ إِلَهَ إِلاَّ اللهُ وَحْدَهُ لاَ شَرِيكَ لَهُ لَهُ الْمُلْكُ وَلَهُ الْحَمْدُ وَهُوَ عَلَى كُلِّ شَيْءٍ قَدِيرٌ",
    count: 100,
    benefit: "من قالها مائة مرة كانت له عدل عشر رقاب",
    source: "البخاري ومسلم",
    category: "general"
  },
  {
    id: 2,
    text: "سُبْحَانَ اللهِ وَبِحَمْدِهِ",
    count: 100,
    benefit: "من قالها مائة مرة حطت خطاياه وإن كانت مثل زبد البحر",
    source: "البخاري ومسلم",
    category: "general"
  },
  {
    id: 3,
    text: "أَسْتَغْفِرُ اللهَ الَّذِي لاَ إِلَهَ إِلاَّ هُوَ الْحَيَّ الْقَيُّومَ وَأَتُوبُ إِلَيْهِ",
    count: 100,
    benefit: "سيد الاستغفار",
    source: "أبو داود والترمذي",
    category: "general"
  }
]

// فئات الأذكار
export const azkarCategories: AzkarCategory[] = [
  {
    id: 'morning',
    name: 'أذكار الصباح',
    description: 'أذكار تقال بعد صلاة الفجر حتى الشروق',
    icon: 'sun',
    color: 'yellow',
    time: 'بعد صلاة الفجر حتى الشروق',
    azkar: morningAzkar
  },
  {
    id: 'evening',
    name: 'أذكار المساء',
    description: 'أذكار تقال بعد صلاة العصر حتى المغرب',
    icon: 'moon',
    color: 'blue',
    time: 'بعد صلاة العصر حتى المغرب',
    azkar: eveningAzkar
  },
  {
    id: 'sleep',
    name: 'أذكار النوم',
    description: 'أذكار تقال عند النوم',
    icon: 'moon',
    color: 'purple',
    time: 'عند النوم',
    azkar: sleepAzkar
  },
  {
    id: 'general',
    name: 'أذكار متنوعة',
    description: 'أذكار يمكن قولها في أي وقت',
    icon: 'heart',
    color: 'green',
    azkar: generalAzkar
  }
]

// الحصول على فئة أذكار بالمعرف
export function getAzkarCategory(id: string): AzkarCategory | undefined {
  return azkarCategories.find(category => category.id === id)
}

// الحصول على جميع الأذكار
export function getAllAzkar(): Zikr[] {
  return azkarCategories.flatMap(category => category.azkar)
}

// البحث في الأذكار
export function searchAzkar(query: string): Zikr[] {
  const allAzkar = getAllAzkar()
  return allAzkar.filter(zikr => 
    zikr.text.includes(query) || 
    zikr.benefit.includes(query) ||
    zikr.source.includes(query)
  )
}
