'use client'

import { useState } from 'react'
import { Book<PERSON><PERSON>, Search, Settings, Heart } from 'lucide-react'
import { surahs } from '@/data/quran'
import { SurahReader } from '@/components/SurahReader'
import { QuranSearch } from '@/components/QuranSearch'

export default function QuranPage() {
  const [selectedSurah, setSelectedSurah] = useState<number | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [showSearch, setShowSearch] = useState(false)

  // تصفية السور حسب البحث
  const filteredSurahs = surahs.filter(surah =>
    surah.name.includes(searchTerm) ||
    surah.englishName.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleSearchResult = (surahNumber: number, ayahNumber: number) => {
    setSelectedSurah(surahNumber)
    // في التطبيق الحقيقي سننتقل إلى الآية المحددة
  }

  if (selectedSurah) {
    return (
      <SurahReader
        surahNumber={selectedSurah}
        onBack={() => setSelectedSurah(null)}
      />
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center py-6">
        <h1 className="text-3xl md:text-4xl font-bold text-gray-800 dark:text-white mb-4">
          القرآن الكريم
        </h1>
        <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
          اقرأ وتدبر كلام الله عز وجل بخط عثماني جميل مع إمكانيات متقدمة للقراءة والتفسير
        </p>
      </div>

      {/* Search and Controls */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <div className="flex flex-col md:flex-row gap-4">
          {/* Search Bar */}
          <div className="flex-1 relative">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
            <input
              type="text"
              placeholder="ابحث في القرآن الكريم..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pr-10 pl-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            />
          </div>

          {/* Controls */}
          <div className="flex space-x-2 space-x-reverse">
            <button
              onClick={() => setShowSearch(true)}
              className="flex items-center space-x-2 space-x-reverse px-4 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors duration-200"
            >
              <Search className="h-4 w-4" />
              <span>البحث المتقدم</span>
            </button>
            <button className="flex items-center space-x-2 space-x-reverse px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200">
              <Settings className="h-4 w-4" />
              <span>الإعدادات</span>
            </button>
            <button className="flex items-center space-x-2 space-x-reverse px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200">
              <Heart className="h-4 w-4" />
              <span>المفضلة</span>
            </button>
          </div>
        </div>
      </div>

      {/* Quick Access */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-gradient-to-r from-green-500 to-green-600 text-white p-6 rounded-lg shadow-md">
          <h3 className="text-xl font-semibold mb-2">آخر قراءة</h3>
          <p className="text-green-100">سورة البقرة - الآية 255</p>
          <button className="mt-3 bg-white text-green-600 px-4 py-2 rounded-md hover:bg-green-50 transition-colors duration-200">
            متابعة القراءة
          </button>
        </div>

        <div className="bg-gradient-to-r from-blue-500 to-blue-600 text-white p-6 rounded-lg shadow-md">
          <h3 className="text-xl font-semibold mb-2">الورد اليومي</h3>
          <p className="text-blue-100">جزء عم - 5 صفحات</p>
          <button className="mt-3 bg-white text-blue-600 px-4 py-2 rounded-md hover:bg-blue-50 transition-colors duration-200">
            بدء الورد
          </button>
        </div>

        <div className="bg-gradient-to-r from-purple-500 to-purple-600 text-white p-6 rounded-lg shadow-md">
          <h3 className="text-xl font-semibold mb-2">التفسير</h3>
          <p className="text-purple-100">تفسير السعدي</p>
          <button className="mt-3 bg-white text-purple-600 px-4 py-2 rounded-md hover:bg-purple-50 transition-colors duration-200">
            قراءة التفسير
          </button>
        </div>
      </div>

      {/* Surahs List */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md">
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-2xl font-bold text-gray-800 dark:text-white">فهرس السور</h2>
        </div>
        
        <div className="divide-y divide-gray-200 dark:divide-gray-700">
          {filteredSurahs.map((surah) => (
            <div
              key={surah.number}
              onClick={() => setSelectedSurah(surah.number)}
              className="p-4 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer transition-colors duration-200"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4 space-x-reverse">
                  <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 rounded-full flex items-center justify-center font-semibold">
                    {surah.number}
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-800 dark:text-white">
                      {surah.name}
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {surah.englishName} • {surah.numberOfAyahs} آية • {surah.revelationTypeArabic}
                    </p>
                  </div>
                </div>
                <BookOpen className="h-5 w-5 text-gray-400" />
              </div>
            </div>
          ))}
        </div>

        {filteredSurahs.length === 0 && (
          <div className="p-8 text-center">
            <Search className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600 dark:text-gray-400">
              لم يتم العثور على نتائج للبحث "{searchTerm}"
            </p>
          </div>
        )}

        {/* Load More */}
        <div className="p-6 text-center border-t border-gray-200 dark:border-gray-700">
          <button className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200">
            عرض المزيد من السور
          </button>
        </div>
      </div>

      {/* Search Modal */}
      <QuranSearch
        isOpen={showSearch}
        onClose={() => setShowSearch(false)}
        onResultClick={handleSearchResult}
      />
    </div>
  )
}
