'use client'

import { useLocalStorage } from './useLocalStorage'

export interface FavoriteItem {
  id: string
  type: 'ayah' | 'zikr' | 'surah'
  title: string
  content: string
  source?: string
  dateAdded: string
}

export function useFavorites() {
  const [favorites, setFavorites] = useLocalStorage<FavoriteItem[]>('noor-al-iman-favorites', [])

  const addToFavorites = (item: Omit<FavoriteItem, 'dateAdded'>) => {
    const newItem: FavoriteItem = {
      ...item,
      dateAdded: new Date().toISOString()
    }
    
    // Check if item already exists
    const exists = favorites.some(fav => fav.id === item.id && fav.type === item.type)
    if (!exists) {
      setFavorites(prev => [...prev, newItem])
      return true
    }
    return false
  }

  const removeFromFavorites = (id: string, type: FavoriteItem['type']) => {
    setFavorites(prev => prev.filter(item => !(item.id === id && item.type === type)))
  }

  const isFavorite = (id: string, type: FavoriteItem['type']) => {
    return favorites.some(item => item.id === id && item.type === type)
  }

  const getFavoritesByType = (type: FavoriteItem['type']) => {
    return favorites.filter(item => item.type === type)
  }

  const clearAllFavorites = () => {
    setFavorites([])
  }

  return {
    favorites,
    addToFavorites,
    removeFromFavorites,
    isFavorite,
    getFavoritesByType,
    clearAllFavorites
  }
}
