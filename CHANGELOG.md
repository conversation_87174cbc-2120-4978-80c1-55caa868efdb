# سجل التغييرات

جميع التغييرات المهمة في هذا المشروع سيتم توثيقها في هذا الملف.

التنسيق مبني على [Keep a Changelog](https://keepachangelog.com/ar/1.0.0/)،
وهذا المشروع يتبع [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [1.0.0] - 2024-12-22

### أضيف
- **القرآن الكريم**
  - عرض جميع السور مع فهرس تفاعلي
  - قارئ القرآن مع إعدادات متقدمة للخط والحجم
  - البحث المتقدم في القرآن الكريم
  - إعدادات القراءة (حجم الخط، نوع الخط، الثيمات)
  - حفظ آخر قراءة تلقائياً

- **أوقات الصلاة**
  - حساب أوقات الصلاة بدقة حسب الموقع الجغرافي
  - دعم مدن متعددة في العالم الإسلامي
  - طرق حساب متعددة (أم القرى، رابطة العالم الإسلامي، إلخ)
  - بوصلة القبلة التفاعلية مع دعم البوصلة الرقمية
  - العد التنازلي للصلاة القادمة
  - إعدادات التنبيهات لكل صلاة

- **الأذكار**
  - أذكار الصباح والمساء مع المصادر
  - أذكار النوم والأذكار المتنوعة
  - عدادات تفاعلية لكل ذكر
  - تتبع التقدم اليومي
  - إعدادات الخط والحجم

- **المميزات المتقدمة**
  - نظام المفضلة للآيات والأذكار
  - تتبع التقدم والإحصائيات الشخصية
  - سلسلة الأذكار اليومية
  - الإشارات المرجعية في القرآن
  - تصدير واستيراد البيانات

- **التنبيهات والإشعارات**
  - تنبيهات أوقات الصلاة
  - تذكيرات الأذكار اليومية
  - تذكير القراءة اليومية
  - إعدادات مخصصة لكل نوع تنبيه

- **واجهة المستخدم**
  - تصميم عصري ومتجاوب
  - دعم الوضع الليلي والنهاري
  - دعم كامل للغة العربية (RTL)
  - تنقل سهل وسريع
  - أيقونات واضحة ومفهومة

- **الإعدادات**
  - إعدادات عامة للتطبيق
  - إعدادات التنبيهات المتقدمة
  - إعدادات المظهر والألوان
  - إدارة البيانات والنسخ الاحتياطي

- **PWA (Progressive Web App)**
  - إمكانية تثبيت التطبيق على الجهاز
  - عمل بدون إنترنت (offline)
  - أيقونات مخصصة للتطبيق
  - اختصارات سريعة

- **الأداء والأمان**
  - تحسينات الأداء والسرعة
  - حماية البيانات الشخصية
  - تشفير البيانات المحلية
  - تحسين SEO

### تقني
- **Frontend**: Next.js 14 مع TypeScript
- **Styling**: Tailwind CSS
- **Icons**: Lucide React
- **Fonts**: خط أميري للنصوص العربية
- **Storage**: Local Storage للبيانات
- **PWA**: دعم كامل لـ Progressive Web App

### الملفات المضافة
- `src/app/` - صفحات التطبيق الرئيسية
- `src/components/` - المكونات المشتركة
- `src/data/` - بيانات القرآن والأذكار
- `src/hooks/` - React Hooks مخصصة
- `src/services/` - خدمات التطبيق
- `public/manifest.json` - ملف PWA
- `run.bat` و `run.sh` - ملفات التشغيل السريع

### معروف
- البيانات حالياً محلية (لا تتطلب إنترنت)
- أوقات الصلاة محسوبة رياضياً (ستتحسن مع API خارجي)
- التشغيل الصوتي سيضاف في الإصدارات القادمة

---

## خطط الإصدارات القادمة

### [1.1.0] - مخطط
- إضافة API حقيقي لأوقات الصلاة
- تشغيل صوتي للقرآن والأذكار
- تفسير القرآن الكريم
- تحسينات في الأداء

### [1.2.0] - مخطط
- تطبيق موبايل (React Native)
- مشاركة على وسائل التواصل
- نظام النسخ الاحتياطي السحابي
- دعم لغات إضافية

---

## كيفية المساهمة

نرحب بمساهماتكم! يرجى قراءة [دليل المساهمة](CONTRIBUTING.md) قبل البدء.

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

---

**"وَمَن يَعْمَلْ مِثْقَالَ ذَرَّةٍ خَيْرًا يَرَهُ"** - الزلزلة: 7
