'use client'

import { useState, useEffect } from 'react'
import { <PERSON>, <PERSON>O<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Setting<PERSON>, Check, X } from 'lucide-react'
import { notificationService, scheduleAllReminders } from '@/services/notifications'
import { useLocalStorage } from '@/hooks/useLocalStorage'

interface NotificationPreferences {
  enabled: boolean
  prayers: {
    fajr: boolean
    dhuhr: boolean
    asr: boolean
    maghrib: boolean
    isha: boolean
  }
  azkar: {
    morning: boolean
    evening: boolean
  }
  reading: boolean
  reminderBefore: number // minutes before prayer
}

const defaultPreferences: NotificationPreferences = {
  enabled: false,
  prayers: {
    fajr: true,
    dhuhr: true,
    asr: true,
    maghrib: true,
    isha: true
  },
  azkar: {
    morning: true,
    evening: true
  },
  reading: true,
  reminderBefore: 5
}

export function NotificationSettings() {
  const [preferences, setPreferences] = useLocalStorage<NotificationPreferences>('notification-preferences', defaultPreferences)
  const [permission, setPermission] = useState<NotificationPermission>('default')
  const [isSupported, setIsSupported] = useState(false)
  const [showSuccess, setShowSuccess] = useState(false)

  useEffect(() => {
    setIsSupported(notificationService.isSupported())
    setPermission(notificationService.getPermission())
  }, [])

  const handleEnableNotifications = async () => {
    const granted = await notificationService.requestPermission()
    setPermission(notificationService.getPermission())
    
    if (granted) {
      setPreferences(prev => ({ ...prev, enabled: true }))
      await scheduleAllReminders()
      setShowSuccess(true)
      setTimeout(() => setShowSuccess(false), 3000)
    }
  }

  const handleDisableNotifications = () => {
    setPreferences(prev => ({ ...prev, enabled: false }))
  }

  const handlePrayerToggle = (prayer: keyof NotificationPreferences['prayers']) => {
    setPreferences(prev => ({
      ...prev,
      prayers: {
        ...prev.prayers,
        [prayer]: !prev.prayers[prayer]
      }
    }))
  }

  const handleAzkarToggle = (type: keyof NotificationPreferences['azkar']) => {
    setPreferences(prev => ({
      ...prev,
      azkar: {
        ...prev.azkar,
        [type]: !prev.azkar[type]
      }
    }))
  }

  const handleReadingToggle = () => {
    setPreferences(prev => ({ ...prev, reading: !prev.reading }))
  }

  const handleReminderTimeChange = (minutes: number) => {
    setPreferences(prev => ({ ...prev, reminderBefore: minutes }))
  }

  const testNotification = async () => {
    await notificationService.showNotification({
      title: 'اختبار التنبيه',
      body: 'هذا اختبار للتأكد من عمل التنبيهات بشكل صحيح',
      tag: 'test-notification'
    })
  }

  if (!isSupported) {
    return (
      <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-6">
        <div className="flex items-center space-x-3 space-x-reverse">
          <BellOff className="h-6 w-6 text-yellow-600 dark:text-yellow-400" />
          <div>
            <h3 className="font-semibold text-yellow-800 dark:text-yellow-200">
              التنبيهات غير مدعومة
            </h3>
            <p className="text-yellow-700 dark:text-yellow-300 mt-1">
              متصفحك لا يدعم التنبيهات. جرب استخدام متصفح حديث مثل Chrome أو Firefox.
            </p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3 space-x-reverse">
          <Bell className="h-6 w-6 text-blue-600 dark:text-blue-400" />
          <h2 className="text-2xl font-bold text-gray-800 dark:text-white">
            إعدادات التنبيهات
          </h2>
        </div>
        
        {showSuccess && (
          <div className="flex items-center space-x-2 space-x-reverse bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-200 px-3 py-1 rounded-lg">
            <Check className="h-4 w-4" />
            <span className="text-sm">تم تفعيل التنبيهات بنجاح</span>
          </div>
        )}
      </div>

      {/* Permission Status */}
      <div className={`p-4 rounded-lg border ${
        permission === 'granted' 
          ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800'
          : permission === 'denied'
          ? 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800'
          : 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800'
      }`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3 space-x-reverse">
            {permission === 'granted' ? (
              <Check className="h-5 w-5 text-green-600 dark:text-green-400" />
            ) : permission === 'denied' ? (
              <X className="h-5 w-5 text-red-600 dark:text-red-400" />
            ) : (
              <Bell className="h-5 w-5 text-yellow-600 dark:text-yellow-400" />
            )}
            <div>
              <h3 className={`font-semibold ${
                permission === 'granted' 
                  ? 'text-green-800 dark:text-green-200'
                  : permission === 'denied'
                  ? 'text-red-800 dark:text-red-200'
                  : 'text-yellow-800 dark:text-yellow-200'
              }`}>
                {permission === 'granted' 
                  ? 'التنبيهات مفعلة'
                  : permission === 'denied'
                  ? 'التنبيهات محظورة'
                  : 'التنبيهات غير مفعلة'
                }
              </h3>
              <p className={`text-sm ${
                permission === 'granted' 
                  ? 'text-green-700 dark:text-green-300'
                  : permission === 'denied'
                  ? 'text-red-700 dark:text-red-300'
                  : 'text-yellow-700 dark:text-yellow-300'
              }`}>
                {permission === 'granted' 
                  ? 'ستتلقى التنبيهات حسب إعداداتك'
                  : permission === 'denied'
                  ? 'يجب السماح بالتنبيهات من إعدادات المتصفح'
                  : 'اضغط على "تفعيل التنبيهات" للبدء'
                }
              </p>
            </div>
          </div>
          
          {permission !== 'granted' && (
            <button
              onClick={handleEnableNotifications}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200"
            >
              تفعيل التنبيهات
            </button>
          )}
        </div>
      </div>

      {/* Main Toggle */}
      {permission === 'granted' && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-gray-800 dark:text-white">
                تفعيل جميع التنبيهات
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                تشغيل أو إيقاف جميع التنبيهات
              </p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={preferences.enabled}
                onChange={(e) => e.target.checked ? handleEnableNotifications() : handleDisableNotifications()}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
            </label>
          </div>
        </div>
      )}

      {/* Prayer Notifications */}
      {preferences.enabled && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
          <div className="flex items-center space-x-3 space-x-reverse mb-4">
            <Clock className="h-5 w-5 text-blue-600 dark:text-blue-400" />
            <h3 className="text-lg font-semibold text-gray-800 dark:text-white">
              تنبيهات أوقات الصلاة
            </h3>
          </div>

          <div className="space-y-3">
            {Object.entries(preferences.prayers).map(([prayer, enabled]) => (
              <div key={prayer} className="flex items-center justify-between">
                <span className="text-gray-700 dark:text-gray-300 capitalize">
                  {prayer === 'fajr' ? 'الفجر' :
                   prayer === 'dhuhr' ? 'الظهر' :
                   prayer === 'asr' ? 'العصر' :
                   prayer === 'maghrib' ? 'المغرب' :
                   'العشاء'}
                </span>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={enabled}
                    onChange={() => handlePrayerToggle(prayer as keyof NotificationPreferences['prayers'])}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                </label>
              </div>
            ))}

            <div className="pt-3 border-t border-gray-200 dark:border-gray-700">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                التذكير قبل الصلاة بـ
              </label>
              <select
                value={preferences.reminderBefore}
                onChange={(e) => handleReminderTimeChange(Number(e.target.value))}
                className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              >
                <option value={0}>عند الوقت مباشرة</option>
                <option value={5}>5 دقائق</option>
                <option value={10}>10 دقائق</option>
                <option value={15}>15 دقيقة</option>
                <option value={30}>30 دقيقة</option>
              </select>
            </div>
          </div>
        </div>
      )}

      {/* Azkar Notifications */}
      {preferences.enabled && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
          <div className="flex items-center space-x-3 space-x-reverse mb-4">
            <Heart className="h-5 w-5 text-purple-600 dark:text-purple-400" />
            <h3 className="text-lg font-semibold text-gray-800 dark:text-white">
              تنبيهات الأذكار
            </h3>
          </div>

          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-gray-700 dark:text-gray-300">أذكار الصباح (6:00 ص)</span>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={preferences.azkar.morning}
                  onChange={() => handleAzkarToggle('morning')}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
              </label>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-gray-700 dark:text-gray-300">أذكار المساء (5:00 م)</span>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={preferences.azkar.evening}
                  onChange={() => handleAzkarToggle('evening')}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
              </label>
            </div>
          </div>
        </div>
      )}

      {/* Reading Reminder */}
      {preferences.enabled && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3 space-x-reverse">
              <BookOpen className="h-5 w-5 text-green-600 dark:text-green-400" />
              <div>
                <h3 className="text-lg font-semibold text-gray-800 dark:text-white">
                  تذكير القراءة اليومية
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  تذكير يومي في الساعة 8:00 مساءً
                </p>
              </div>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={preferences.reading}
                onChange={handleReadingToggle}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
            </label>
          </div>
        </div>
      )}

      {/* Test Notification */}
      {preferences.enabled && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-gray-800 dark:text-white">
                اختبار التنبيهات
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                تأكد من عمل التنبيهات بشكل صحيح
              </p>
            </div>
            <button
              onClick={testNotification}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200"
            >
              إرسال تنبيه تجريبي
            </button>
          </div>
        </div>
      )}
    </div>
  )
}
