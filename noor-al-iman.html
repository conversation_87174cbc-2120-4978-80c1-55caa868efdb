<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نور الإيمان - تطبيق إسلامي متكامل</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            direction: rtl;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 40px 0;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .card:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.2);
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .card-icon {
            font-size: 3rem;
            margin-bottom: 15px;
        }

        .card h3 {
            font-size: 1.5rem;
            margin-bottom: 10px;
        }

        .card p {
            opacity: 0.8;
        }

        .prayer-times {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .prayer-times h2 {
            text-align: center;
            margin-bottom: 20px;
            font-size: 1.8rem;
        }

        .prayer-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }

        .prayer-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .prayer-item:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .prayer-item.next {
            background: rgba(255, 255, 255, 0.3);
            border: 2px solid rgba(255, 255, 255, 0.5);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .prayer-name {
            font-weight: bold;
            margin-bottom: 5px;
            font-size: 1.1rem;
        }

        .prayer-time {
            font-size: 1.2rem;
            font-family: monospace;
            font-weight: bold;
        }

        .verse-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-bottom: 30px;
        }

        .verse-text {
            font-size: 1.8rem;
            line-height: 1.8;
            margin-bottom: 15px;
            font-weight: 300;
        }

        .verse-reference {
            opacity: 0.8;
            font-size: 1rem;
        }

        .section {
            display: none;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            margin-top: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .section.active {
            display: block;
            animation: fadeIn 0.5s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .back-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin-bottom: 20px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateX(-5px);
        }

        .surah-list {
            display: grid;
            gap: 10px;
        }

        .surah-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 8px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .surah-item:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateX(-5px);
        }

        .azkar-categories {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .azkar-category {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .azkar-category:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-5px);
        }

        .azkar-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 15px;
        }

        .azkar-text {
            font-size: 1.2rem;
            line-height: 1.8;
            margin-bottom: 10px;
        }

        .azkar-benefit {
            opacity: 0.8;
            font-size: 0.9rem;
            margin-bottom: 10px;
        }

        .counter {
            display: inline-block;
            background: rgba(255, 255, 255, 0.2);
            width: 50px;
            height: 50px;
            border-radius: 50%;
            line-height: 50px;
            text-align: center;
            cursor: pointer;
            font-weight: bold;
            font-size: 1.2rem;
            transition: all 0.3s ease;
        }

        .counter:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        .footer {
            text-align: center;
            margin-top: 50px;
            padding: 30px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }

            .cards {
                grid-template-columns: 1fr;
            }

            .prayer-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            body {
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- الصفحة الرئيسية -->
        <div id="home">
            <div class="header">
                <h1>🌙 نور الإيمان</h1>
                <p>تطبيق إسلامي متكامل للقرآن الكريم وأوقات الصلاة والأذكار</p>
            </div>

            <div class="cards">
                <div class="card" onclick="showSection('quran')">
                    <div class="card-icon">📖</div>
                    <h3>القرآن الكريم</h3>
                    <p>اقرأ وتدبر كلام الله عز وجل</p>
                </div>

                <div class="card" onclick="showSection('prayer')">
                    <div class="card-icon">🕐</div>
                    <h3>أوقات الصلاة</h3>
                    <p>مواقيت الصلاة الدقيقة</p>
                </div>

                <div class="card" onclick="showSection('azkar')">
                    <div class="card-icon">🤲</div>
                    <h3>الأذكار</h3>
                    <p>أذكار الصباح والمساء</p>
                </div>

                <div class="card" onclick="showFavorites()">
                    <div class="card-icon">⭐</div>
                    <h3>المفضلة</h3>
                    <p>آياتك وأذكارك المحفوظة</p>
                </div>
            </div>

            <div class="prayer-times">
                <h2>🕐 مواقيت الصلاة اليوم - الرياض</h2>
                <div class="prayer-grid">
                    <div class="prayer-item">
                        <div class="prayer-name">🌅 الفجر</div>
                        <div class="prayer-time">05:15</div>
                    </div>
                    <div class="prayer-item">
                        <div class="prayer-name">☀️ الظهر</div>
                        <div class="prayer-time">12:30</div>
                    </div>
                    <div class="prayer-item next">
                        <div class="prayer-name">🌤️ العصر</div>
                        <div class="prayer-time">15:45</div>
                        <div style="font-size: 0.8rem; margin-top: 5px;">الصلاة القادمة</div>
                    </div>
                    <div class="prayer-item">
                        <div class="prayer-name">🌅 المغرب</div>
                        <div class="prayer-time">18:20</div>
                    </div>
                    <div class="prayer-item">
                        <div class="prayer-name">🌙 العشاء</div>
                        <div class="prayer-time">19:50</div>
                    </div>
                </div>
            </div>

            <div class="verse-section">
                <h2>📖 آية اليوم</h2>
                <div class="verse-text">
                    وَمَن يَتَّقِ اللَّهَ يَجْعَل لَّهُ مَخْرَجًا
                </div>
                <div class="verse-reference">
                    سورة الطلاق - الآية 2
                </div>
            </div>
        </div>

        <!-- صفحة القرآن -->
        <div id="quran" class="section">
            <button class="back-btn" onclick="showSection('home')">← العودة للرئيسية</button>
            <h2>📖 القرآن الكريم</h2>
            <div class="surah-list">
                <div class="surah-item" onclick="showSurah('الفاتحة', 7)">
                    <div>
                        <strong>1. الفاتحة</strong><br>
                        <small>Al-Fatihah • 7 آيات • مكية</small>
                    </div>
                    <div>📖</div>
                </div>
                <div class="surah-item" onclick="showSurah('البقرة', 286)">
                    <div>
                        <strong>2. البقرة</strong><br>
                        <small>Al-Baqarah • 286 آية • مدنية</small>
                    </div>
                    <div>📖</div>
                </div>
                <div class="surah-item" onclick="showSurah('آل عمران', 200)">
                    <div>
                        <strong>3. آل عمران</strong><br>
                        <small>Aal-E-Imran • 200 آية • مدنية</small>
                    </div>
                    <div>📖</div>
                </div>
                <div class="surah-item" onclick="showSurah('النساء', 176)">
                    <div>
                        <strong>4. النساء</strong><br>
                        <small>An-Nisa • 176 آية • مدنية</small>
                    </div>
                    <div>📖</div>
                </div>
                <div class="surah-item" onclick="showSurah('المائدة', 120)">
                    <div>
                        <strong>5. المائدة</strong><br>
                        <small>Al-Maidah • 120 آية • مدنية</small>
                    </div>
                    <div>📖</div>
                </div>
                <div class="surah-item" onclick="showSurah('الأنعام', 165)">
                    <div>
                        <strong>6. الأنعام</strong><br>
                        <small>Al-Anam • 165 آية • مكية</small>
                    </div>
                    <div>📖</div>
                </div>
                <div class="surah-item" onclick="showSurah('الأعراف', 206)">
                    <div>
                        <strong>7. الأعراف</strong><br>
                        <small>Al-Araf • 206 آية • مكية</small>
                    </div>
                    <div>📖</div>
                </div>
                <div class="surah-item" onclick="showSurah('الأنفال', 75)">
                    <div>
                        <strong>8. الأنفال</strong><br>
                        <small>Al-Anfal • 75 آية • مدنية</small>
                    </div>
                    <div>📖</div>
                </div>
                <div class="surah-item" onclick="showSurah('التوبة', 129)">
                    <div>
                        <strong>9. التوبة</strong><br>
                        <small>At-Tawbah • 129 آية • مدنية</small>
                    </div>
                    <div>📖</div>
                </div>
                <div class="surah-item" onclick="showSurah('يونس', 109)">
                    <div>
                        <strong>10. يونس</strong><br>
                        <small>Yunus • 109 آية • مكية</small>
                    </div>
                    <div>📖</div>
                </div>
            </div>
        </div>

        <!-- صفحة أوقات الصلاة -->
        <div id="prayer" class="section">
            <button class="back-btn" onclick="showSection('home')">← العودة للرئيسية</button>
            <h2>🕐 أوقات الصلاة - الرياض</h2>
            <div class="prayer-grid" style="margin-bottom: 30px;">
                <div class="prayer-item">
                    <div class="prayer-name">🌅 الفجر</div>
                    <div class="prayer-time">05:15</div>
                    <div style="font-size: 0.8rem; margin-top: 5px;">انتهت</div>
                </div>
                <div class="prayer-item">
                    <div class="prayer-name">☀️ الظهر</div>
                    <div class="prayer-time">12:30</div>
                    <div style="font-size: 0.8rem; margin-top: 5px;">انتهت</div>
                </div>
                <div class="prayer-item next">
                    <div class="prayer-name">🌤️ العصر</div>
                    <div class="prayer-time">15:45</div>
                    <div style="font-size: 0.8rem; margin-top: 5px;">بعد ساعتين</div>
                </div>
                <div class="prayer-item">
                    <div class="prayer-name">🌅 المغرب</div>
                    <div class="prayer-time">18:20</div>
                    <div style="font-size: 0.8rem; margin-top: 5px;">بعد 5 ساعات</div>
                </div>
                <div class="prayer-item">
                    <div class="prayer-name">🌙 العشاء</div>
                    <div class="prayer-time">19:50</div>
                    <div style="font-size: 0.8rem; margin-top: 5px;">بعد 6 ساعات</div>
                </div>
            </div>

            <div style="text-align: center; background: rgba(255,255,255,0.1); padding: 30px; border-radius: 15px;">
                <h3>🧭 اتجاه القبلة</h3>
                <div style="width: 120px; height: 120px; margin: 20px auto; background: rgba(255,255,255,0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 3rem; position: relative;">
                    <div style="position: absolute; top: 10px; font-size: 1rem;">شمال</div>
                    <div style="position: absolute; bottom: 10px; font-size: 1rem;">جنوب</div>
                    <div style="position: absolute; left: 10px; font-size: 1rem;">شرق</div>
                    <div style="position: absolute; right: 10px; font-size: 1rem;">غرب</div>
                    <div style="transform: rotate(45deg); font-size: 2rem;">↑</div>
                </div>
                <p><strong>شمال شرق (45°)</strong></p>
                <p style="opacity: 0.8; margin-top: 10px;">المسافة إلى مكة: 1,234 كم</p>
            </div>
        </div>

        <!-- صفحة الأذكار -->
        <div id="azkar" class="section">
            <button class="back-btn" onclick="showSection('home')">← العودة للرئيسية</button>
            <h2>🤲 الأذكار</h2>

            <div class="azkar-categories">
                <div class="azkar-category" onclick="showAzkar('morning')">
                    <div style="font-size: 2rem; margin-bottom: 10px;">☀️</div>
                    <h3>أذكار الصباح</h3>
                    <p>بعد صلاة الفجر حتى الشروق</p>
                </div>
                <div class="azkar-category" onclick="showAzkar('evening')">
                    <div style="font-size: 2rem; margin-bottom: 10px;">🌙</div>
                    <h3>أذكار المساء</h3>
                    <p>بعد صلاة العصر حتى المغرب</p>
                </div>
                <div class="azkar-category" onclick="showAzkar('sleep')">
                    <div style="font-size: 2rem; margin-bottom: 10px;">😴</div>
                    <h3>أذكار النوم</h3>
                    <p>عند النوم</p>
                </div>
                <div class="azkar-category" onclick="showAzkar('general')">
                    <div style="font-size: 2rem; margin-bottom: 10px;">🔄</div>
                    <h3>أذكار متنوعة</h3>
                    <p>في كل وقت</p>
                </div>
            </div>

            <div id="azkar-content"></div>
        </div>

        <div class="footer">
            <h3>🌙 نور الإيمان</h3>
            <p>جعل الله هذا العمل في ميزان حسناتنا وحسناتكم</p>
            <p style="opacity: 0.7; margin-top: 10px;">الإصدار 1.0.0 - ديسمبر 2024</p>
        </div>
    </div>

    <script>
        // إظهار الأقسام
        function showSection(sectionId) {
            // إخفاء جميع الأقسام
            document.getElementById('home').style.display = 'none';
            document.querySelectorAll('.section').forEach(section => {
                section.classList.remove('active');
            });

            // إظهار القسم المطلوب
            if (sectionId === 'home') {
                document.getElementById('home').style.display = 'block';
            } else {
                document.getElementById(sectionId).classList.add('active');
            }
        }

        // عرض السورة
        function showSurah(name, verses) {
            alert(`📖 سورة ${name}\n\nعدد الآيات: ${verses}\n\nسيتم إضافة النص الكامل قريباً إن شاء الله`);
        }

        // عرض الأذكار
        function showAzkar(type) {
            const content = document.getElementById('azkar-content');

            if (type === 'morning') {
                content.innerHTML = `
                    <h3 style="margin-bottom: 20px;">☀️ أذكار الصباح</h3>
                    <div class="azkar-item">
                        <div class="azkar-text">
                            أَعُوذُ بِاللهِ مِنْ الشَّيْطَانِ الرَّجِيمِ اللّهُ لاَ إِلَـهَ إِلاَّ هُوَ الْحَيُّ الْقَيُّومُ لاَ تَأْخُذُهُ سِنَةٌ وَلاَ نَوْمٌ لَّهُ مَا فِي السَّمَاوَاتِ وَمَا فِي الأَرْضِ
                        </div>
                        <div class="azkar-benefit">
                            <strong>الفائدة:</strong> من قرأها في بداية النهار أجير من الجن حتى يمسي
                        </div>
                        <div style="text-align: center; margin-top: 15px;">
                            <span class="counter" onclick="decrementCounter(this, 1)">1</span>
                        </div>
                    </div>
                    <div class="azkar-item">
                        <div class="azkar-text">
                            بِسْمِ اللهِ الَّذِي لاَ يَضُرُّ مَعَ اسْمِهِ شَيْءٌ فِي الأَرْضِ وَلاَ فِي السَّمَاء وَهُوَ السَّمِيعُ الْعَلِيمُ
                        </div>
                        <div class="azkar-benefit">
                            <strong>الفائدة:</strong> من قالها ثلاثاً لم تصبه فجأة بلاء حتى يمسي
                        </div>
                        <div style="text-align: center; margin-top: 15px;">
                            <span class="counter" onclick="decrementCounter(this, 3)">3</span>
                        </div>
                    </div>
                    <div class="azkar-item">
                        <div class="azkar-text">
                            رَضِيتُ بِاللهِ رَبَّاً وَبِالإِسْلاَمِ دِيناً وَبِمُحَمَّدٍ صَلَّى اللهُ عَلَيْهِ وَسَلَّمَ رَسُولاً
                        </div>
                        <div class="azkar-benefit">
                            <strong>الفائدة:</strong> حق على الله أن يرضيه يوم القيامة
                        </div>
                        <div style="text-align: center; margin-top: 15px;">
                            <span class="counter" onclick="decrementCounter(this, 3)">3</span>
                        </div>
                    </div>
                `;
            } else if (type === 'evening') {
                content.innerHTML = `
                    <h3 style="margin-bottom: 20px;">🌙 أذكار المساء</h3>
                    <div class="azkar-item">
                        <div class="azkar-text">
                            أَمْسَيْنَا وَأَمْسَى الْمُلْكُ لِلهِ وَالْحَمْدُ لِلهِ لاَ إِلَهَ إِلاَّ اللهُ وَحْدَهُ لاَ شَرِيكَ لَهُ لَهُ الْمُلْكُ وَلَهُ الْحَمْدُ وَهُوَ عَلَى كُلِّ شَيْءٍ قَدِيرٌ
                        </div>
                        <div class="azkar-benefit">
                            <strong>الفائدة:</strong> دعاء شامل للحماية والخير
                        </div>
                        <div style="text-align: center; margin-top: 15px;">
                            <span class="counter" onclick="decrementCounter(this, 1)">1</span>
                        </div>
                    </div>
                    <div class="azkar-item">
                        <div class="azkar-text">
                            اللَّهُمَّ بِكَ أَمْسَيْنَا وَبِكَ أَصْبَحْنَا وَبِكَ نَحْيَا وَبِكَ نَمُوتُ وَإِلَيْكَ الْمَصِيرُ
                        </div>
                        <div class="azkar-benefit">
                            <strong>الفائدة:</strong> إقرار بالتوحيد والتوكل على الله
                        </div>
                        <div style="text-align: center; margin-top: 15px;">
                            <span class="counter" onclick="decrementCounter(this, 1)">1</span>
                        </div>
                    </div>
                `;
            } else if (type === 'sleep') {
                content.innerHTML = `
                    <h3 style="margin-bottom: 20px;">😴 أذكار النوم</h3>
                    <div class="azkar-item">
                        <div class="azkar-text">
                            بِاسْمِكَ رَبِّي وَضَعْتُ جَنْبِي وَبِكَ أَرْفَعُهُ فَإِن أَمْسَكْتَ نَفْسِي فَارْحَمْهَا وَإِنْ أَرْسَلْتَهَا فَاحْفَظْهَا بِمَا تَحْفَظُ بِهِ عِبَادَكَ الصَّالِحِينَ
                        </div>
                        <div class="azkar-benefit">
                            <strong>الفائدة:</strong> دعاء عند النوم للحفظ والرحمة
                        </div>
                        <div style="text-align: center; margin-top: 15px;">
                            <span class="counter" onclick="decrementCounter(this, 1)">1</span>
                        </div>
                    </div>
                `;
            } else if (type === 'general') {
                content.innerHTML = `
                    <h3 style="margin-bottom: 20px;">🔄 أذكار متنوعة</h3>
                    <div class="azkar-item">
                        <div class="azkar-text">
                            سُبْحَانَ اللهِ وَبِحَمْدِهِ
                        </div>
                        <div class="azkar-benefit">
                            <strong>الفائدة:</strong> من قالها مائة مرة حطت خطاياه وإن كانت مثل زبد البحر
                        </div>
                        <div style="text-align: center; margin-top: 15px;">
                            <span class="counter" onclick="decrementCounter(this, 100)">100</span>
                        </div>
                    </div>
                    <div class="azkar-item">
                        <div class="azkar-text">
                            لاَ إِلَهَ إِلاَّ اللهُ وَحْدَهُ لاَ شَرِيكَ لَهُ لَهُ الْمُلْكُ وَلَهُ الْحَمْدُ وَهُوَ عَلَى كُلِّ شَيْءٍ قَدِيرٌ
                        </div>
                        <div class="azkar-benefit">
                            <strong>الفائدة:</strong> من قالها مائة مرة كانت له عدل عشر رقاب
                        </div>
                        <div style="text-align: center; margin-top: 15px;">
                            <span class="counter" onclick="decrementCounter(this, 100)">100</span>
                        </div>
                    </div>
                `;
            }
        }

        // تقليل العداد
        function decrementCounter(element, originalCount) {
            let currentCount = parseInt(element.innerHTML);
            if (currentCount > 0) {
                currentCount--;
                element.innerHTML = currentCount;

                // تغيير اللون عند الانتهاء
                if (currentCount === 0) {
                    element.style.background = 'rgba(34, 197, 94, 0.3)';
                    element.style.color = '#22c55e';
                    setTimeout(() => {
                        alert('🎉 بارك الله فيك! تم إكمال هذا الذكر');
                    }, 500);
                }
            } else {
                // إعادة تعيين العداد
                element.innerHTML = originalCount;
                element.style.background = 'rgba(255, 255, 255, 0.2)';
                element.style.color = 'white';
            }
        }

        // عرض المفضلة
        function showFavorites() {
            alert('⭐ المفضلة\n\nهنا ستجد آياتك وأذكارك المفضلة\n\nسيتم إضافة هذه الميزة قريباً إن شاء الله');
        }

        // تحديث الوقت
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('ar-SA');

            // يمكن إضافة عرض الوقت الحالي هنا
        }

        // رسالة ترحيب
        setTimeout(() => {
            alert('🌙 أهلاً وسهلاً بك في نور الإيمان!\n\nتطبيق إسلامي متكامل يجمع بين:\n📖 القرآن الكريم\n🕐 أوقات الصلاة\n🤲 الأذكار\n⭐ المفضلة\n\nبارك الله فيك! 🤲');
        }, 1000);

        // تشغيل تحديث الوقت كل دقيقة
        setInterval(updateTime, 60000);
    </script>
</body>
</html>