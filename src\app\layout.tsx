import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { Navigation } from '@/components/Navigation'
import { ThemeProvider } from '@/components/ThemeProvider'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'نور الإيمان - تطبيق إسلامي متكامل',
  description: 'تطبيق إسلامي شامل يحتوي على القرآن الكريم وأوقات الصلاة والأذكار',
  keywords: 'قرآن, صلاة, أذكار, إسلام, مسلم, القرآن الكريم, أوقات الصلاة, أذكار الصباح, أذكار المساء',
  authors: [{ name: 'نور الإيمان' }],
  viewport: 'width=device-width, initial-scale=1',
  manifest: '/manifest.json',
  themeColor: '#2563eb',
  appleWebApp: {
    capable: true,
    statusBarStyle: 'default',
    title: 'نور الإيمان'
  },
  openGraph: {
    title: 'نور الإيمان - تطبيق إسلامي متكامل',
    description: 'تطبيق إسلامي شامل يحتوي على القرآن الكريم وأوقات الصلاة والأذكار',
    type: 'website',
    locale: 'ar_SA',
    siteName: 'نور الإيمان'
  },
  twitter: {
    card: 'summary_large_image',
    title: 'نور الإيمان - تطبيق إسلامي متكامل',
    description: 'تطبيق إسلامي شامل يحتوي على القرآن الكريم وأوقات الصلاة والأذكار'
  }
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="ar" dir="rtl">
      <body className={`${inter.className} min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800`}>
        <ThemeProvider>
          <div className="flex flex-col min-h-screen">
            <Navigation />
            <main className="flex-1 container mx-auto px-4 py-6">
              {children}
            </main>
          </div>
        </ThemeProvider>
      </body>
    </html>
  )
}
