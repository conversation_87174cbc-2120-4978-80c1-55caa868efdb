'use client'

import { useState, useEffect } from 'react'
import { BookOpen, Volume2 } from 'lucide-react'

interface Verse {
  arabic: string
  translation: string
  surah: string
  ayah: number
  surahNumber: number
}

export function QuranVerse() {
  const [verse, setVerse] = useState<Verse | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // آيات مختارة للعرض اليومي
    const selectedVerses: Verse[] = [
      {
        arabic: "وَمَن يَتَّقِ اللَّهَ يَجْعَل لَّهُ مَخْرَجًا ﴿٢﴾ وَيَرْزُقْهُ مِنْ حَيْثُ لَا يَحْتَسِبُ ۚ وَمَن يَتَوَكَّلْ عَلَى اللَّهِ فَهُوَ حَسْبُهُ ۚ إِنَّ اللَّهَ بَالِغُ أَمْرِهِ ۚ قَدْ جَعَلَ اللَّهُ لِكُلِّ شَيْءٍ قَدْرًا",
        translation: "ومن يتق الله يجعل له مخرجًا، ويرزقه من حيث لا يحتسب، ومن يتوكل على الله فهو حسبه، إن الله بالغ أمره، قد جعل الله لكل شيء قدرًا",
        surah: "الطلاق",
        ayah: 2,
        surahNumber: 65
      },
      {
        arabic: "وَلَا تَهِنُوا وَلَا تَحْزَنُوا وَأَنتُمُ الْأَعْلَوْنَ إِن كُنتُم مُّؤْمِنِينَ",
        translation: "ولا تهنوا ولا تحزنوا وأنتم الأعلون إن كنتم مؤمنين",
        surah: "آل عمران",
        ayah: 139,
        surahNumber: 3
      },
      {
        arabic: "فَاذْكُرُونِي أَذْكُرْكُمْ وَاشْكُرُوا لِي وَلَا تَكْفُرُونِ",
        translation: "فاذكروني أذكركم واشكروا لي ولا تكفرون",
        surah: "البقرة",
        ayah: 152,
        surahNumber: 2
      },
      {
        arabic: "وَمَا تَوْفِيقِي إِلَّا بِاللَّهِ ۚ عَلَيْهِ تَوَكَّلْتُ وَإِلَيْهِ أُنِيبُ",
        translation: "وما توفيقي إلا بالله، عليه توكلت وإليه أنيب",
        surah: "هود",
        ayah: 88,
        surahNumber: 11
      },
      {
        arabic: "وَبَشِّرِ الصَّابِرِينَ ﴿١٥٥﴾ الَّذِينَ إِذَا أَصَابَتْهُم مُّصِيبَةٌ قَالُوا إِنَّا لِلَّهِ وَإِنَّا إِلَيْهِ رَاجِعُونَ",
        translation: "وبشر الصابرين، الذين إذا أصابتهم مصيبة قالوا إنا لله وإنا إليه راجعون",
        surah: "البقرة",
        ayah: 155,
        surahNumber: 2
      }
    ]

    // اختيار آية عشوائية أو حسب اليوم
    const today = new Date().getDate()
    const selectedVerse = selectedVerses[today % selectedVerses.length]
    
    setTimeout(() => {
      setVerse(selectedVerse)
      setIsLoading(false)
    }, 1000)
  }, [])

  const handlePlayAudio = () => {
    // في التطبيق الحقيقي سنضيف تشغيل الصوت
    alert('سيتم إضافة تشغيل الصوت قريباً إن شاء الله')
  }

  if (isLoading) {
    return (
      <div className="text-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
        <p className="mt-2 text-gray-600 dark:text-gray-400">جاري التحميل...</p>
      </div>
    )
  }

  if (!verse) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-600 dark:text-gray-400">لم يتم العثور على آية</p>
      </div>
    )
  }

  return (
    <div className="text-center space-y-6">
      {/* Arabic Verse */}
      <div className="quran-text text-2xl md:text-3xl leading-relaxed text-gray-800 dark:text-white p-6 bg-white dark:bg-gray-800 rounded-lg shadow-sm">
        {verse.arabic}
      </div>

      {/* Verse Info */}
      <div className="flex items-center justify-center space-x-4 space-x-reverse text-sm text-gray-600 dark:text-gray-400">
        <div className="flex items-center space-x-2 space-x-reverse">
          <BookOpen className="h-4 w-4" />
          <span>سورة {verse.surah}</span>
        </div>
        <span>•</span>
        <span>الآية {verse.ayah}</span>
      </div>

      {/* Audio Button */}
      <div className="flex justify-center">
        <button
          onClick={handlePlayAudio}
          className="flex items-center space-x-2 space-x-reverse px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200"
        >
          <Volume2 className="h-4 w-4" />
          <span>استمع للآية</span>
        </button>
      </div>

      {/* Share and Favorite Buttons */}
      <div className="flex justify-center space-x-4 space-x-reverse">
        <button className="px-4 py-2 bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 rounded-lg hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors duration-200">
          مشاركة
        </button>
        <button className="px-4 py-2 bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-300 rounded-lg hover:bg-red-200 dark:hover:bg-red-800 transition-colors duration-200">
          إضافة للمفضلة
        </button>
      </div>
    </div>
  )
}
