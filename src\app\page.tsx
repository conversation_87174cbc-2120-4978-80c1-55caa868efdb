import { <PERSON>, <PERSON>O<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from 'lucide-react'
import Link from 'next/link'
import { PrayerTimes } from '@/components/PrayerTimes'
import { QuranVerse } from '@/components/QuranVerse'
import { NewsUpdates } from '@/components/NewsUpdates'

export default function Home() {
  return (
    <div className="space-y-8">
      {/* Header Section */}
      <div className="text-center py-8">
        <h1 className="text-4xl md:text-6xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600 mb-4">
          نور الإيمان
        </h1>
        <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
          تطبيق إسلامي متكامل يجمع بين القرآن الكريم وأوقات الصلاة والأذكار في مكان واحد
        </p>
      </div>

      {/* Quick Access Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Link href="/quran" className="group">
          <div className="prayer-card group-hover:scale-105 transform transition-all duration-300">
            <div className="flex items-center space-x-4 space-x-reverse">
              <div className="p-3 bg-green-100 dark:bg-green-900 rounded-full">
                <BookOpen className="h-8 w-8 text-green-600 dark:text-green-400" />
              </div>
              <div>
                <h3 className="text-xl font-semibold text-gray-800 dark:text-white">القرآن الكريم</h3>
                <p className="text-gray-600 dark:text-gray-300">اقرأ وتدبر كلام الله</p>
              </div>
            </div>
          </div>
        </Link>

        <Link href="/prayer-times" className="group">
          <div className="prayer-card group-hover:scale-105 transform transition-all duration-300">
            <div className="flex items-center space-x-4 space-x-reverse">
              <div className="p-3 bg-blue-100 dark:bg-blue-900 rounded-full">
                <Clock className="h-8 w-8 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <h3 className="text-xl font-semibold text-gray-800 dark:text-white">أوقات الصلاة</h3>
                <p className="text-gray-600 dark:text-gray-300">مواقيت الصلاة بدقة</p>
              </div>
            </div>
          </div>
        </Link>

        <Link href="/azkar" className="group">
          <div className="prayer-card group-hover:scale-105 transform transition-all duration-300">
            <div className="flex items-center space-x-4 space-x-reverse">
              <div className="p-3 bg-purple-100 dark:bg-purple-900 rounded-full">
                <Heart className="h-8 w-8 text-purple-600 dark:text-purple-400" />
              </div>
              <div>
                <h3 className="text-xl font-semibold text-gray-800 dark:text-white">الأذكار</h3>
                <p className="text-gray-600 dark:text-gray-300">أذكار الصباح والمساء</p>
              </div>
            </div>
          </div>
        </Link>

        <Link href="/favorites" className="group">
          <div className="prayer-card group-hover:scale-105 transform transition-all duration-300">
            <div className="flex items-center space-x-4 space-x-reverse">
              <div className="p-3 bg-red-100 dark:bg-red-900 rounded-full">
                <Star className="h-8 w-8 text-red-600 dark:text-red-400" />
              </div>
              <div>
                <h3 className="text-xl font-semibold text-gray-800 dark:text-white">المفضلة</h3>
                <p className="text-gray-600 dark:text-gray-300">آياتك وأذكارك المحفوظة</p>
              </div>
            </div>
          </div>
        </Link>

        <Link href="/stats" className="group">
          <div className="prayer-card group-hover:scale-105 transform transition-all duration-300">
            <div className="flex items-center space-x-4 space-x-reverse">
              <div className="p-3 bg-indigo-100 dark:bg-indigo-900 rounded-full">
                <BarChart3 className="h-8 w-8 text-indigo-600 dark:text-indigo-400" />
              </div>
              <div>
                <h3 className="text-xl font-semibold text-gray-800 dark:text-white">الإحصائيات</h3>
                <p className="text-gray-600 dark:text-gray-300">تتبع تقدمك اليومي</p>
              </div>
            </div>
          </div>
        </Link>
      </div>

      {/* Prayer Times Widget */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-6 text-center">
          أوقات الصلاة اليوم
        </h2>
        <PrayerTimes />
      </div>

      {/* Daily Verse */}
      <div className="bg-gradient-to-r from-green-50 to-blue-50 dark:from-gray-800 dark:to-gray-700 rounded-xl shadow-lg p-6">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-6 text-center">
          آية اليوم
        </h2>
        <QuranVerse />
      </div>

      {/* News Updates Component */}
      <NewsUpdates />
    </div>
  )
}
