'use client'

export interface NotificationOptions {
  title: string
  body: string
  icon?: string
  badge?: string
  tag?: string
  requireInteraction?: boolean
  actions?: NotificationAction[]
}

export class NotificationService {
  private static instance: NotificationService
  private permission: NotificationPermission = 'default'

  private constructor() {
    if (typeof window !== 'undefined' && 'Notification' in window) {
      this.permission = Notification.permission
    }
  }

  static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService()
    }
    return NotificationService.instance
  }

  async requestPermission(): Promise<boolean> {
    if (!('Notification' in window)) {
      console.log('This browser does not support notifications')
      return false
    }

    if (this.permission === 'granted') {
      return true
    }

    if (this.permission === 'denied') {
      return false
    }

    const permission = await Notification.requestPermission()
    this.permission = permission
    return permission === 'granted'
  }

  async showNotification(options: NotificationOptions): Promise<boolean> {
    if (!await this.requestPermission()) {
      return false
    }

    try {
      const notification = new Notification(options.title, {
        body: options.body,
        icon: options.icon || '/icon-192x192.png',
        badge: options.badge || '/icon-192x192.png',
        tag: options.tag,
        requireInteraction: options.requireInteraction || false,
        dir: 'rtl',
        lang: 'ar'
      })

      // Auto close after 5 seconds if not requiring interaction
      if (!options.requireInteraction) {
        setTimeout(() => {
          notification.close()
        }, 5000)
      }

      return true
    } catch (error) {
      console.error('Error showing notification:', error)
      return false
    }
  }

  // Prayer time notifications
  async schedulePrayerNotification(prayerName: string, timeUntil: number): Promise<void> {
    if (timeUntil <= 0) return

    setTimeout(async () => {
      await this.showNotification({
        title: `حان وقت ${prayerName}`,
        body: `حان الآن وقت صلاة ${prayerName}. بارك الله فيك`,
        icon: '/prayer-icon.png',
        tag: `prayer-${prayerName}`,
        requireInteraction: true
      })
    }, timeUntil)
  }

  // Azkar reminder notifications
  async scheduleAzkarReminder(type: 'morning' | 'evening'): Promise<void> {
    const now = new Date()
    const targetTime = new Date()

    if (type === 'morning') {
      targetTime.setHours(6, 0, 0, 0) // 6 AM
      if (targetTime <= now) {
        targetTime.setDate(targetTime.getDate() + 1)
      }
    } else {
      targetTime.setHours(17, 0, 0, 0) // 5 PM
      if (targetTime <= now) {
        targetTime.setDate(targetTime.getDate() + 1)
      }
    }

    const timeUntil = targetTime.getTime() - now.getTime()

    setTimeout(async () => {
      await this.showNotification({
        title: type === 'morning' ? 'أذكار الصباح' : 'أذكار المساء',
        body: `حان وقت ${type === 'morning' ? 'أذكار الصباح' : 'أذكار المساء'}. لا تنس ذكر الله`,
        icon: '/azkar-icon.png',
        tag: `azkar-${type}`,
        requireInteraction: false
      })

      // Schedule next day
      this.scheduleAzkarReminder(type)
    }, timeUntil)
  }

  // Reading reminder
  async scheduleReadingReminder(): Promise<void> {
    const now = new Date()
    const targetTime = new Date()
    targetTime.setHours(20, 0, 0, 0) // 8 PM

    if (targetTime <= now) {
      targetTime.setDate(targetTime.getDate() + 1)
    }

    const timeUntil = targetTime.getTime() - now.getTime()

    setTimeout(async () => {
      await this.showNotification({
        title: 'تذكير القراءة',
        body: 'حان وقت قراءة القرآن الكريم. اجعل من القراءة عادة يومية',
        icon: '/quran-icon.png',
        tag: 'reading-reminder',
        requireInteraction: false
      })

      // Schedule next day
      this.scheduleReadingReminder()
    }, timeUntil)
  }

  // Generic reminder
  async scheduleReminder(
    title: string,
    body: string,
    delayMinutes: number,
    tag?: string
  ): Promise<void> {
    const delayMs = delayMinutes * 60 * 1000

    setTimeout(async () => {
      await this.showNotification({
        title,
        body,
        tag: tag || 'reminder',
        requireInteraction: false
      })
    }, delayMs)
  }

  // Check if notifications are supported
  isSupported(): boolean {
    return typeof window !== 'undefined' && 'Notification' in window
  }

  // Get current permission status
  getPermission(): NotificationPermission {
    return this.permission
  }

  // Clear all notifications with specific tag
  clearNotifications(tag: string): void {
    // This is limited in browsers, but we can try
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.ready.then(registration => {
        registration.getNotifications({ tag }).then(notifications => {
          notifications.forEach(notification => notification.close())
        })
      })
    }
  }
}

// Export singleton instance
export const notificationService = NotificationService.getInstance()

// Utility functions
export const scheduleAllReminders = async () => {
  const service = NotificationService.getInstance()
  
  if (await service.requestPermission()) {
    // Schedule azkar reminders
    await service.scheduleAzkarReminder('morning')
    await service.scheduleAzkarReminder('evening')
    
    // Schedule reading reminder
    await service.scheduleReadingReminder()
    
    console.log('All reminders scheduled successfully')
  } else {
    console.log('Notification permission denied')
  }
}

export const showPrayerNotification = async (prayerName: string) => {
  const service = NotificationService.getInstance()
  
  await service.showNotification({
    title: `حان وقت ${prayerName}`,
    body: `حان الآن وقت صلاة ${prayerName}. بارك الله فيك`,
    requireInteraction: true,
    tag: `prayer-${prayerName}`
  })
}

export const showAzkarReminder = async (type: 'morning' | 'evening') => {
  const service = NotificationService.getInstance()
  
  await service.showNotification({
    title: type === 'morning' ? 'أذكار الصباح' : 'أذكار المساء',
    body: `حان وقت ${type === 'morning' ? 'أذكار الصباح' : 'أذكار المساء'}. لا تنس ذكر الله`,
    tag: `azkar-${type}`
  })
}
