<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نور الإيمان - تطبيق إسلامي متكامل</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&display=swap" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'arabic': ['Amir<PERSON>', 'serif'],
                    }
                }
            }
        }
    </script>
    <style>
        .arabic-text {
            font-family: '<PERSON><PERSON>', serif;
            line-height: 2;
        }
        .quran-text {
            font-family: '<PERSON><PERSON>', serif;
            line-height: 2.5;
        }
        .prayer-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .dark .gradient-bg {
            background: linear-gradient(135deg, #1e3a8a 0%, #3730a3 100%);
        }
    </style>
</head>
<body class="bg-gray-50 dark:bg-gray-900 font-arabic">
    <!-- Navigation -->
    <nav class="bg-white dark:bg-gray-800 shadow-lg sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex items-center space-x-3 space-x-reverse">
                    <div class="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
                        <span class="text-white font-bold text-xl">ن</span>
                    </div>
                    <span class="text-xl font-bold text-gray-800 dark:text-white">نور الإيمان</span>
                </div>

                <!-- Navigation Links -->
                <div class="hidden md:flex space-x-8 space-x-reverse">
                    <a href="#home" class="nav-link text-blue-600 dark:text-blue-400 font-medium">الرئيسية</a>
                    <a href="#quran" class="nav-link text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400">القرآن الكريم</a>
                    <a href="#prayer" class="nav-link text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400">أوقات الصلاة</a>
                    <a href="#azkar" class="nav-link text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400">الأذكار</a>
                </div>

                <!-- Theme Toggle -->
                <button id="theme-toggle" class="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300">
                    <span id="theme-icon">🌙</span>
                </button>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Home Section -->
        <section id="home" class="space-y-8">
            <!-- Hero Section -->
            <div class="text-center py-12 gradient-bg rounded-2xl text-white">
                <h1 class="text-4xl md:text-6xl font-bold mb-4">نور الإيمان</h1>
                <p class="text-xl md:text-2xl mb-8 opacity-90">تطبيق إسلامي متكامل</p>
                <p class="text-lg opacity-80 max-w-2xl mx-auto">
                    يجمع بين القرآن الكريم وأوقات الصلاة والأذكار في تطبيق واحد سهل الاستخدام
                </p>
            </div>

            <!-- Quick Access Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 hover:scale-105 transform transition-all duration-300 cursor-pointer" onclick="showSection('quran')">
                    <div class="flex items-center space-x-4 space-x-reverse">
                        <div class="p-3 bg-green-100 dark:bg-green-900 rounded-full">
                            <span class="text-2xl">📖</span>
                        </div>
                        <div>
                            <h3 class="text-xl font-semibold text-gray-800 dark:text-white">القرآن الكريم</h3>
                            <p class="text-gray-600 dark:text-gray-300">اقرأ وتدبر كلام الله</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 hover:scale-105 transform transition-all duration-300 cursor-pointer" onclick="showSection('prayer')">
                    <div class="flex items-center space-x-4 space-x-reverse">
                        <div class="p-3 bg-blue-100 dark:bg-blue-900 rounded-full">
                            <span class="text-2xl">🕐</span>
                        </div>
                        <div>
                            <h3 class="text-xl font-semibold text-gray-800 dark:text-white">أوقات الصلاة</h3>
                            <p class="text-gray-600 dark:text-gray-300">مواقيت الصلاة بدقة</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 hover:scale-105 transform transition-all duration-300 cursor-pointer" onclick="showSection('azkar')">
                    <div class="flex items-center space-x-4 space-x-reverse">
                        <div class="p-3 bg-purple-100 dark:bg-purple-900 rounded-full">
                            <span class="text-2xl">🤲</span>
                        </div>
                        <div>
                            <h3 class="text-xl font-semibold text-gray-800 dark:text-white">الأذكار</h3>
                            <p class="text-gray-600 dark:text-gray-300">أذكار الصباح والمساء</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 hover:scale-105 transform transition-all duration-300 cursor-pointer">
                    <div class="flex items-center space-x-4 space-x-reverse">
                        <div class="p-3 bg-red-100 dark:bg-red-900 rounded-full">
                            <span class="text-2xl">⭐</span>
                        </div>
                        <div>
                            <h3 class="text-xl font-semibold text-gray-800 dark:text-white">المفضلة</h3>
                            <p class="text-gray-600 dark:text-gray-300">آياتك وأذكارك المحفوظة</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Today's Prayer Times -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
                <h2 class="text-2xl font-bold text-gray-800 dark:text-white mb-6 text-center">
                    مواقيت الصلاة اليوم
                </h2>
                <div class="grid grid-cols-2 md:grid-cols-5 gap-4" id="prayer-times">
                    <!-- Prayer times will be populated by JavaScript -->
                </div>
            </div>

            <!-- Verse of the Day -->
            <div class="bg-gradient-to-r from-green-50 to-blue-50 dark:from-gray-800 dark:to-gray-700 rounded-xl shadow-lg p-8">
                <h2 class="text-2xl font-bold text-gray-800 dark:text-white mb-6 text-center">
                    آية اليوم
                </h2>
                <div class="text-center">
                    <div class="quran-text text-2xl md:text-3xl text-gray-800 dark:text-white mb-4">
                        وَمَن يَتَّقِ اللَّهَ يَجْعَل لَّهُ مَخْرَجًا
                    </div>
                    <p class="text-gray-600 dark:text-gray-400 mb-2">
                        "ومن يتق الله يجعل له مخرجاً"
                    </p>
                    <p class="text-blue-600 dark:text-blue-400 font-semibold">
                        سورة الطلاق - الآية 2
                    </p>
                </div>
            </div>
        </section>

        <!-- Quran Section -->
        <section id="quran" class="space-y-6 hidden">
            <div class="text-center py-6">
                <h1 class="text-3xl md:text-4xl font-bold text-gray-800 dark:text-white mb-4">
                    القرآن الكريم
                </h1>
                <p class="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                    اقرأ وتدبر كلام الله عز وجل
                </p>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg">
                <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                    <div class="flex flex-col md:flex-row gap-4">
                        <div class="flex-1 relative">
                            <input
                                type="text"
                                placeholder="ابحث في القرآن الكريم..."
                                class="w-full pr-10 pl-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                            />
                            <span class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400">🔍</span>
                        </div>
                    </div>
                </div>

                <div class="divide-y divide-gray-200 dark:divide-gray-700" id="surah-list">
                    <!-- Surahs will be populated by JavaScript -->
                </div>
            </div>
        </section>

        <!-- Prayer Times Section -->
        <section id="prayer" class="space-y-6 hidden">
            <div class="text-center py-6">
                <h1 class="text-3xl md:text-4xl font-bold text-gray-800 dark:text-white mb-4">
                    أوقات الصلاة
                </h1>
                <p class="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                    مواقيت الصلاة الدقيقة حسب موقعك الجغرافي
                </p>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
                <h2 class="text-2xl font-bold text-gray-800 dark:text-white mb-6 text-center">
                    مواقيت اليوم - الرياض
                </h2>
                <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4" id="detailed-prayer-times">
                    <!-- Detailed prayer times will be populated by JavaScript -->
                </div>
            </div>

            <!-- Qibla Direction -->
            <div class="bg-gradient-to-r from-green-50 to-blue-50 dark:from-gray-800 dark:to-gray-700 rounded-xl shadow-lg p-6">
                <div class="text-center">
                    <h3 class="text-2xl font-bold text-gray-800 dark:text-white mb-4">
                        اتجاه القبلة
                    </h3>
                    <div class="w-32 h-32 mx-auto bg-white dark:bg-gray-800 rounded-full shadow-lg flex items-center justify-center mb-4">
                        <div class="w-2 h-16 bg-green-600 rounded-full transform rotate-45"></div>
                    </div>
                    <p class="text-gray-600 dark:text-gray-300 mb-4">
                        اتجاه القبلة من موقعك الحالي: <strong>شمال شرق (45°)</strong>
                    </p>
                </div>
            </div>
        </section>

        <!-- Azkar Section -->
        <section id="azkar" class="space-y-6 hidden">
            <div class="text-center py-6">
                <h1 class="text-3xl md:text-4xl font-bold text-gray-800 dark:text-white mb-4">
                    الأذكار
                </h1>
                <p class="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                    أذكار الصباح والمساء وأذكار متنوعة لحفظ المسلم
                </p>
            </div>

            <!-- Azkar Categories -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div class="bg-gradient-to-r from-yellow-400 to-orange-500 text-white p-6 rounded-lg shadow-md text-center cursor-pointer" onclick="showAzkarCategory('morning')">
                    <div class="text-4xl mb-2">☀️</div>
                    <h3 class="font-semibold">أذكار الصباح</h3>
                    <p class="text-sm opacity-90">بعد صلاة الفجر</p>
                </div>

                <div class="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-6 rounded-lg shadow-md text-center cursor-pointer" onclick="showAzkarCategory('evening')">
                    <div class="text-4xl mb-2">🌙</div>
                    <h3 class="font-semibold">أذكار المساء</h3>
                    <p class="text-sm opacity-90">بعد صلاة العصر</p>
                </div>

                <div class="bg-gradient-to-r from-green-500 to-teal-600 text-white p-6 rounded-lg shadow-md text-center cursor-pointer">
                    <div class="text-4xl mb-2">😴</div>
                    <h3 class="font-semibold">أذكار النوم</h3>
                    <p class="text-sm opacity-90">قبل النوم</p>
                </div>

                <div class="bg-gradient-to-r from-red-500 to-pink-600 text-white p-6 rounded-lg shadow-md text-center cursor-pointer">
                    <div class="text-4xl mb-2">🔄</div>
                    <h3 class="font-semibold">أذكار متنوعة</h3>
                    <p class="text-sm opacity-90">في كل وقت</p>
                </div>
            </div>

            <!-- Azkar Content -->
            <div id="azkar-content" class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 hidden">
                <!-- Azkar content will be populated by JavaScript -->
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 dark:bg-gray-900 text-white py-8 mt-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <p class="text-lg mb-2">نور الإيمان - تطبيق إسلامي متكامل</p>
            <p class="text-gray-400">جعل الله هذا العمل في ميزان حسناتنا وحسناتكم 🤲</p>
            <p class="text-sm text-gray-500 mt-4">الإصدار 1.0.0 - ديسمبر 2024</p>
        </div>
    </footer>

    <script>
        // Theme Toggle
        const themeToggle = document.getElementById('theme-toggle');
        const themeIcon = document.getElementById('theme-icon');
        const html = document.documentElement;

        let isDark = localStorage.getItem('theme') === 'dark';
        
        function updateTheme() {
            if (isDark) {
                html.classList.add('dark');
                themeIcon.textContent = '☀️';
                localStorage.setItem('theme', 'dark');
            } else {
                html.classList.remove('dark');
                themeIcon.textContent = '🌙';
                localStorage.setItem('theme', 'light');
            }
        }

        updateTheme();

        themeToggle.addEventListener('click', () => {
            isDark = !isDark;
            updateTheme();
        });

        // Navigation
        function showSection(sectionId) {
            // Hide all sections
            const sections = ['home', 'quran', 'prayer', 'azkar'];
            sections.forEach(id => {
                document.getElementById(id).classList.add('hidden');
            });

            // Show selected section
            document.getElementById(sectionId).classList.remove('hidden');

            // Update navigation links
            const links = document.querySelectorAll('.nav-link');
            links.forEach(link => {
                link.classList.remove('text-blue-600', 'dark:text-blue-400');
                link.classList.add('text-gray-600', 'dark:text-gray-300');
            });

            // Highlight active link
            const activeLink = document.querySelector(`a[href="#${sectionId}"]`);
            if (activeLink) {
                activeLink.classList.remove('text-gray-600', 'dark:text-gray-300');
                activeLink.classList.add('text-blue-600', 'dark:text-blue-400');
            }
        }

        // Add click events to navigation links
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const sectionId = link.getAttribute('href').substring(1);
                showSection(sectionId);
            });
        });

        // Prayer Times
        function generatePrayerTimes() {
            const prayers = [
                { name: 'الفجر', time: '05:15', icon: '🌅' },
                { name: 'الظهر', time: '12:30', icon: '☀️' },
                { name: 'العصر', time: '15:45', icon: '🌤️' },
                { name: 'المغرب', time: '18:20', icon: '🌅' },
                { name: 'العشاء', time: '19:50', icon: '🌙' }
            ];

            const prayerTimesContainer = document.getElementById('prayer-times');
            const detailedPrayerTimesContainer = document.getElementById('detailed-prayer-times');

            prayers.forEach((prayer, index) => {
                const isNext = index === 2; // العصر كمثال للصلاة القادمة
                
                // For home page
                const prayerCard = `
                    <div class="text-center p-4 ${isNext ? 'bg-blue-100 dark:bg-blue-900 border-2 border-blue-300 dark:border-blue-700 scale-105' : 'bg-gray-50 dark:bg-gray-700'} rounded-lg">
                        <div class="text-2xl mb-2">${prayer.icon}</div>
                        <h3 class="font-semibold ${isNext ? 'text-blue-800 dark:text-blue-300' : 'text-gray-800 dark:text-white'}">${prayer.name}</h3>
                        <p class="text-lg font-mono ${isNext ? 'text-blue-600 dark:text-blue-400' : 'text-gray-600 dark:text-gray-300'}">${prayer.time}</p>
                        ${isNext ? '<p class="text-xs text-blue-500 dark:text-blue-400 mt-1">بعد ساعتين</p>' : ''}
                    </div>
                `;
                
                prayerTimesContainer.innerHTML += prayerCard;
                detailedPrayerTimesContainer.innerHTML += prayerCard;
            });
        }

        // Surahs
        function generateSurahs() {
            const surahs = [
                { number: 1, name: 'الفاتحة', englishName: 'Al-Fatihah', verses: 7, type: 'مكية' },
                { number: 2, name: 'البقرة', englishName: 'Al-Baqarah', verses: 286, type: 'مدنية' },
                { number: 3, name: 'آل عمران', englishName: 'Aal-E-Imran', verses: 200, type: 'مدنية' },
                { number: 4, name: 'النساء', englishName: 'An-Nisa', verses: 176, type: 'مدنية' },
                { number: 5, name: 'المائدة', englishName: 'Al-Maidah', verses: 120, type: 'مدنية' },
                { number: 6, name: 'الأنعام', englishName: 'Al-Anam', verses: 165, type: 'مكية' },
                { number: 7, name: 'الأعراف', englishName: 'Al-Araf', verses: 206, type: 'مكية' },
                { number: 8, name: 'الأنفال', englishName: 'Al-Anfal', verses: 75, type: 'مدنية' },
                { number: 9, name: 'التوبة', englishName: 'At-Tawbah', verses: 129, type: 'مدنية' },
                { number: 10, name: 'يونس', englishName: 'Yunus', verses: 109, type: 'مكية' }
            ];

            const surahListContainer = document.getElementById('surah-list');
            
            surahs.forEach(surah => {
                const surahCard = `
                    <div class="p-4 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer transition-colors duration-200">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-4 space-x-reverse">
                                <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 rounded-full flex items-center justify-center font-semibold">
                                    ${surah.number}
                                </div>
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-800 dark:text-white">
                                        ${surah.name}
                                    </h3>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">
                                        ${surah.englishName} • ${surah.verses} آية • ${surah.type}
                                    </p>
                                </div>
                            </div>
                            <span class="text-2xl">📖</span>
                        </div>
                    </div>
                `;
                surahListContainer.innerHTML += surahCard;
            });
        }

        // Azkar
        function showAzkarCategory(category) {
            const azkarContent = document.getElementById('azkar-content');
            azkarContent.classList.remove('hidden');
            
            const morningAzkar = [
                {
                    text: "أَعُوذُ بِاللهِ مِنْ الشَّيْطَانِ الرَّجِيمِ اللّهُ لاَ إِلَـهَ إِلاَّ هُوَ الْحَيُّ الْقَيُّومُ",
                    count: 1,
                    benefit: "من قرأها في بداية النهار أجير من الجن حتى يمسي"
                },
                {
                    text: "بِسْمِ اللهِ الَّذِي لاَ يَضُرُّ مَعَ اسْمِهِ شَيْءٌ فِي الأَرْضِ وَلاَ فِي السَّمَاء وَهُوَ السَّمِيعُ الْعَلِيمُ",
                    count: 3,
                    benefit: "من قالها ثلاثاً لم تصبه فجأة بلاء حتى يمسي"
                }
            ];

            const eveningAzkar = [
                {
                    text: "أَمْسَيْنَا وَأَمْسَى الْمُلْكُ لِلهِ وَالْحَمْدُ لِلهِ لاَ إِلَهَ إِلاَّ اللهُ وَحْدَهُ لاَ شَرِيكَ لَهُ",
                    count: 1,
                    benefit: "دعاء شامل للحماية والخير"
                }
            ];

            const azkarData = category === 'morning' ? morningAzkar : eveningAzkar;
            const title = category === 'morning' ? 'أذكار الصباح' : 'أذكار المساء';
            
            let content = `
                <h2 class="text-2xl font-bold text-gray-800 dark:text-white mb-6">${title}</h2>
                <div class="space-y-6">
            `;

            azkarData.forEach((zikr, index) => {
                content += `
                    <div class="border-b border-gray-200 dark:border-gray-700 pb-6">
                        <div class="quran-text text-lg leading-relaxed text-gray-800 dark:text-white mb-3">
                            ${zikr.text}
                        </div>
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">
                                    <strong>الفائدة:</strong> ${zikr.benefit}
                                </p>
                                <p class="text-sm text-blue-600 dark:text-blue-400">
                                    <strong>العدد:</strong> ${zikr.count} ${zikr.count === 1 ? 'مرة' : 'مرات'}
                                </p>
                            </div>
                            <div class="text-center">
                                <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 rounded-full flex items-center justify-center text-lg font-semibold cursor-pointer hover:scale-110 transition-transform">
                                    ${zikr.count}
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });

            content += '</div>';
            azkarContent.innerHTML = content;
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            generatePrayerTimes();
            generateSurahs();
        });
    </script>
</body>
</html>
