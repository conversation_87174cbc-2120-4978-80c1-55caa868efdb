'use client'

import { useState } from 'react'
import { Bell, X, Star, Gift, Zap, Calendar } from 'lucide-react'

interface NewsItem {
  id: string
  title: string
  content: string
  type: 'feature' | 'update' | 'announcement' | 'tip'
  date: string
  isNew: boolean
}

const newsItems: NewsItem[] = [
  {
    id: '1',
    title: 'مرحباً بك في نور الإيمان!',
    content: 'تطبيق إسلامي متكامل يجمع بين القرآن الكريم وأوقات الصلاة والأذكار. نسأل الله أن ينفع به المسلمين في كل مكان.',
    type: 'announcement',
    date: '2024-12-22',
    isNew: true
  },
  {
    id: '2',
    title: 'ميزة جديدة: البحث المتقدم',
    content: 'يمكنك الآن البحث في القرآن الكريم بكلمات مفتاحية والعثور على الآيات بسهولة أكبر.',
    type: 'feature',
    date: '2024-12-22',
    isNew: true
  },
  {
    id: '3',
    title: 'نصيحة: استخدم المفضلة',
    content: 'احفظ آياتك وأذكارك المفضلة للوصول إليها بسرعة. اضغط على أيقونة القلب لإضافة أي محتوى للمفضلة.',
    type: 'tip',
    date: '2024-12-22',
    isNew: false
  },
  {
    id: '4',
    title: 'تحديث: تحسينات في الأداء',
    content: 'تم تحسين سرعة التطبيق وإصلاح بعض المشاكل الصغيرة لتجربة أفضل.',
    type: 'update',
    date: '2024-12-21',
    isNew: false
  }
]

export function NewsUpdates() {
  const [isOpen, setIsOpen] = useState(false)
  const [dismissedItems, setDismissedItems] = useState<string[]>([])

  const visibleItems = newsItems.filter(item => !dismissedItems.includes(item.id))
  const hasNewItems = visibleItems.some(item => item.isNew)

  const dismissItem = (id: string) => {
    setDismissedItems(prev => [...prev, id])
  }

  const getIcon = (type: NewsItem['type']) => {
    switch (type) {
      case 'feature': return <Star className="h-4 w-4" />
      case 'update': return <Zap className="h-4 w-4" />
      case 'announcement': return <Gift className="h-4 w-4" />
      case 'tip': return <Calendar className="h-4 w-4" />
      default: return <Bell className="h-4 w-4" />
    }
  }

  const getColor = (type: NewsItem['type']) => {
    switch (type) {
      case 'feature': return 'text-yellow-600 dark:text-yellow-400 bg-yellow-100 dark:bg-yellow-900/20'
      case 'update': return 'text-blue-600 dark:text-blue-400 bg-blue-100 dark:bg-blue-900/20'
      case 'announcement': return 'text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/20'
      case 'tip': return 'text-purple-600 dark:text-purple-400 bg-purple-100 dark:bg-purple-900/20'
      default: return 'text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-900/20'
    }
  }

  const getTypeLabel = (type: NewsItem['type']) => {
    switch (type) {
      case 'feature': return 'ميزة جديدة'
      case 'update': return 'تحديث'
      case 'announcement': return 'إعلان'
      case 'tip': return 'نصيحة'
      default: return 'خبر'
    }
  }

  if (visibleItems.length === 0) {
    return null
  }

  return (
    <>
      {/* Notification Bell */}
      <button
        onClick={() => setIsOpen(true)}
        className="fixed bottom-6 left-6 z-50 p-3 bg-blue-600 text-white rounded-full shadow-lg hover:bg-blue-700 transition-all duration-200 hover:scale-110"
      >
        <Bell className="h-6 w-6" />
        {hasNewItems && (
          <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
        )}
      </button>

      {/* News Modal */}
      {isOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-2xl max-w-2xl w-full max-h-[80vh] overflow-hidden">
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center space-x-3 space-x-reverse">
                <Bell className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                <h2 className="text-2xl font-bold text-gray-800 dark:text-white">
                  الأخبار والتحديثات
                </h2>
              </div>
              <button
                onClick={() => setIsOpen(false)}
                className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors duration-200"
              >
                <X className="h-5 w-5 text-gray-600 dark:text-gray-400" />
              </button>
            </div>

            {/* Content */}
            <div className="overflow-y-auto max-h-96">
              {visibleItems.map((item) => (
                <div
                  key={item.id}
                  className="p-6 border-b border-gray-200 dark:border-gray-700 last:border-b-0"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 space-x-reverse mb-3">
                        <div className={`p-1 rounded-full ${getColor(item.type)}`}>
                          {getIcon(item.type)}
                        </div>
                        <span className={`text-xs font-medium px-2 py-1 rounded-full ${getColor(item.type)}`}>
                          {getTypeLabel(item.type)}
                        </span>
                        {item.isNew && (
                          <span className="text-xs font-medium px-2 py-1 rounded-full bg-red-100 dark:bg-red-900/20 text-red-600 dark:text-red-400">
                            جديد
                          </span>
                        )}
                      </div>
                      
                      <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-2">
                        {item.title}
                      </h3>
                      
                      <p className="text-gray-600 dark:text-gray-300 leading-relaxed mb-3">
                        {item.content}
                      </p>
                      
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        {new Date(item.date).toLocaleDateString('ar-SA')}
                      </p>
                    </div>

                    <button
                      onClick={() => dismissItem(item.id)}
                      className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors duration-200 mr-4"
                      title="إخفاء"
                    >
                      <X className="h-4 w-4 text-gray-400" />
                    </button>
                  </div>
                </div>
              ))}
            </div>

            {/* Footer */}
            <div className="p-6 bg-gray-50 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-600">
              <div className="flex items-center justify-between">
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {visibleItems.length} عنصر
                </p>
                <button
                  onClick={() => setIsOpen(false)}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200"
                >
                  إغلاق
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  )
}
