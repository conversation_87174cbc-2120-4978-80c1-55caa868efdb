<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نور الإيمان - تطبيق إسلامي</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            direction: rtl;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 40px 0;
        }
        
        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            transition: transform 0.3s ease;
            cursor: pointer;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .card:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.2);
        }
        
        .card-icon {
            font-size: 3rem;
            margin-bottom: 15px;
        }
        
        .card h3 {
            font-size: 1.5rem;
            margin-bottom: 10px;
        }
        
        .card p {
            opacity: 0.8;
        }
        
        .prayer-times {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .prayer-times h2 {
            text-align: center;
            margin-bottom: 20px;
            font-size: 1.8rem;
        }
        
        .prayer-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }
        
        .prayer-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        
        .prayer-item.next {
            background: rgba(255, 255, 255, 0.3);
            border: 2px solid rgba(255, 255, 255, 0.5);
        }
        
        .prayer-name {
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .prayer-time {
            font-size: 1.2rem;
            font-family: monospace;
        }
        
        .verse-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .verse-text {
            font-size: 1.8rem;
            line-height: 1.8;
            margin-bottom: 15px;
            font-weight: 300;
        }
        
        .verse-reference {
            opacity: 0.8;
            font-size: 1rem;
        }
        
        .section {
            display: none;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            margin-top: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .section.active {
            display: block;
        }
        
        .back-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            margin-bottom: 20px;
            font-size: 1rem;
        }
        
        .back-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        
        .surah-list {
            display: grid;
            gap: 10px;
        }
        
        .surah-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 8px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
        }
        
        .surah-item:hover {
            background: rgba(255, 255, 255, 0.2);
        }
        
        .azkar-categories {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .azkar-category {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            cursor: pointer;
        }
        
        .azkar-category:hover {
            background: rgba(255, 255, 255, 0.2);
        }
        
        .azkar-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 15px;
        }
        
        .azkar-text {
            font-size: 1.2rem;
            line-height: 1.8;
            margin-bottom: 10px;
        }
        
        .azkar-benefit {
            opacity: 0.8;
            font-size: 0.9rem;
            margin-bottom: 10px;
        }
        
        .counter {
            display: inline-block;
            background: rgba(255, 255, 255, 0.2);
            width: 40px;
            height: 40px;
            border-radius: 50%;
            line-height: 40px;
            text-align: center;
            cursor: pointer;
            font-weight: bold;
        }
        
        .counter:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .cards {
                grid-template-columns: 1fr;
            }
            
            .prayer-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- الصفحة الرئيسية -->
        <div id="home">
            <div class="header">
                <h1>🌙 نور الإيمان</h1>
                <p>تطبيق إسلامي متكامل للقرآن الكريم وأوقات الصلاة والأذكار</p>
            </div>
            
            <div class="cards">
                <div class="card" onclick="showSection('quran')">
                    <div class="card-icon">📖</div>
                    <h3>القرآن الكريم</h3>
                    <p>اقرأ وتدبر كلام الله عز وجل</p>
                </div>
                
                <div class="card" onclick="showSection('prayer')">
                    <div class="card-icon">🕐</div>
                    <h3>أوقات الصلاة</h3>
                    <p>مواقيت الصلاة الدقيقة</p>
                </div>
                
                <div class="card" onclick="showSection('azkar')">
                    <div class="card-icon">🤲</div>
                    <h3>الأذكار</h3>
                    <p>أذكار الصباح والمساء</p>
                </div>
                
                <div class="card" onclick="alert('قريباً إن شاء الله')">
                    <div class="card-icon">⭐</div>
                    <h3>المفضلة</h3>
                    <p>آياتك وأذكارك المحفوظة</p>
                </div>
            </div>
            
            <div class="prayer-times">
                <h2>🕐 مواقيت الصلاة اليوم</h2>
                <div class="prayer-grid">
                    <div class="prayer-item">
                        <div class="prayer-name">الفجر</div>
                        <div class="prayer-time">05:15</div>
                    </div>
                    <div class="prayer-item">
                        <div class="prayer-name">الظهر</div>
                        <div class="prayer-time">12:30</div>
                    </div>
                    <div class="prayer-item next">
                        <div class="prayer-name">العصر</div>
                        <div class="prayer-time">15:45</div>
                        <div style="font-size: 0.8rem; margin-top: 5px;">القادمة</div>
                    </div>
                    <div class="prayer-item">
                        <div class="prayer-name">المغرب</div>
                        <div class="prayer-time">18:20</div>
                    </div>
                    <div class="prayer-item">
                        <div class="prayer-name">العشاء</div>
                        <div class="prayer-time">19:50</div>
                    </div>
                </div>
            </div>
            
            <div class="verse-section">
                <h2>📖 آية اليوم</h2>
                <div class="verse-text">
                    وَمَن يَتَّقِ اللَّهَ يَجْعَل لَّهُ مَخْرَجًا
                </div>
                <div class="verse-reference">
                    سورة الطلاق - الآية 2
                </div>
            </div>
        </div>
        
        <!-- صفحة القرآن -->
        <div id="quran" class="section">
            <button class="back-btn" onclick="showSection('home')">← العودة للرئيسية</button>
            <h2>📖 القرآن الكريم</h2>
            <div class="surah-list">
                <div class="surah-item" onclick="alert('سورة الفاتحة - 7 آيات')">
                    <div>
                        <strong>1. الفاتحة</strong><br>
                        <small>Al-Fatihah • 7 آيات • مكية</small>
                    </div>
                    <div>📖</div>
                </div>
                <div class="surah-item" onclick="alert('سورة البقرة - 286 آية')">
                    <div>
                        <strong>2. البقرة</strong><br>
                        <small>Al-Baqarah • 286 آية • مدنية</small>
                    </div>
                    <div>📖</div>
                </div>
                <div class="surah-item" onclick="alert('سورة آل عمران - 200 آية')">
                    <div>
                        <strong>3. آل عمران</strong><br>
                        <small>Aal-E-Imran • 200 آية • مدنية</small>
                    </div>
                    <div>📖</div>
                </div>
                <div class="surah-item" onclick="alert('سورة النساء - 176 آية')">
                    <div>
                        <strong>4. النساء</strong><br>
                        <small>An-Nisa • 176 آية • مدنية</small>
                    </div>
                    <div>📖</div>
                </div>
                <div class="surah-item" onclick="alert('سورة المائدة - 120 آية')">
                    <div>
                        <strong>5. المائدة</strong><br>
                        <small>Al-Maidah • 120 آية • مدنية</small>
                    </div>
                    <div>📖</div>
                </div>
            </div>
        </div>
        
        <!-- صفحة أوقات الصلاة -->
        <div id="prayer" class="section">
            <button class="back-btn" onclick="showSection('home')">← العودة للرئيسية</button>
            <h2>🕐 أوقات الصلاة - الرياض</h2>
            <div class="prayer-grid" style="margin-bottom: 30px;">
                <div class="prayer-item">
                    <div class="prayer-name">🌅 الفجر</div>
                    <div class="prayer-time">05:15</div>
                </div>
                <div class="prayer-item">
                    <div class="prayer-name">☀️ الظهر</div>
                    <div class="prayer-time">12:30</div>
                </div>
                <div class="prayer-item next">
                    <div class="prayer-name">🌤️ العصر</div>
                    <div class="prayer-time">15:45</div>
                    <div style="font-size: 0.8rem; margin-top: 5px;">بعد ساعتين</div>
                </div>
                <div class="prayer-item">
                    <div class="prayer-name">🌅 المغرب</div>
                    <div class="prayer-time">18:20</div>
                </div>
                <div class="prayer-item">
                    <div class="prayer-name">🌙 العشاء</div>
                    <div class="prayer-time">19:50</div>
                </div>
            </div>
            
            <div style="text-align: center; background: rgba(255,255,255,0.1); padding: 30px; border-radius: 15px;">
                <h3>🧭 اتجاه القبلة</h3>
                <div style="width: 100px; height: 100px; margin: 20px auto; background: rgba(255,255,255,0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 2rem;">
                    ↗️
                </div>
                <p>شمال شرق (45°)</p>
            </div>
        </div>
        
        <!-- صفحة الأذكار -->
        <div id="azkar" class="section">
            <button class="back-btn" onclick="showSection('home')">← العودة للرئيسية</button>
            <h2>🤲 الأذكار</h2>
            
            <div class="azkar-categories">
                <div class="azkar-category" onclick="showAzkar('morning')">
                    <div style="font-size: 2rem; margin-bottom: 10px;">☀️</div>
                    <h3>أذكار الصباح</h3>
                    <p>بعد صلاة الفجر</p>
                </div>
                <div class="azkar-category" onclick="showAzkar('evening')">
                    <div style="font-size: 2rem; margin-bottom: 10px;">🌙</div>
                    <h3>أذكار المساء</h3>
                    <p>بعد صلاة العصر</p>
                </div>
                <div class="azkar-category" onclick="alert('قريباً إن شاء الله')">
                    <div style="font-size: 2rem; margin-bottom: 10px;">😴</div>
                    <h3>أذكار النوم</h3>
                    <p>قبل النوم</p>
                </div>
                <div class="azkar-category" onclick="alert('قريباً إن شاء الله')">
                    <div style="font-size: 2rem; margin-bottom: 10px;">🔄</div>
                    <h3>أذكار متنوعة</h3>
                    <p>في كل وقت</p>
                </div>
            </div>
            
            <div id="azkar-content"></div>
        </div>
    </div>

    <script>
        function showSection(sectionId) {
            // إخفاء جميع الأقسام
            document.getElementById('home').style.display = 'none';
            document.querySelectorAll('.section').forEach(section => {
                section.classList.remove('active');
            });
            
            // إظهار القسم المطلوب
            if (sectionId === 'home') {
                document.getElementById('home').style.display = 'block';
            } else {
                document.getElementById(sectionId).classList.add('active');
            }
        }
        
        function showAzkar(type) {
            const content = document.getElementById('azkar-content');
            
            if (type === 'morning') {
                content.innerHTML = `
                    <h3>☀️ أذكار الصباح</h3>
                    <div class="azkar-item">
                        <div class="azkar-text">
                            أَعُوذُ بِاللهِ مِنْ الشَّيْطَانِ الرَّجِيمِ اللّهُ لاَ إِلَـهَ إِلاَّ هُوَ الْحَيُّ الْقَيُّومُ
                        </div>
                        <div class="azkar-benefit">
                            الفائدة: من قرأها في بداية النهار أجير من الجن حتى يمسي
                        </div>
                        <div style="text-align: center;">
                            <span class="counter" onclick="this.innerHTML = parseInt(this.innerHTML) > 0 ? parseInt(this.innerHTML) - 1 : 0">1</span>
                        </div>
                    </div>
                    <div class="azkar-item">
                        <div class="azkar-text">
                            بِسْمِ اللهِ الَّذِي لاَ يَضُرُّ مَعَ اسْمِهِ شَيْءٌ فِي الأَرْضِ وَلاَ فِي السَّمَاء وَهُوَ السَّمِيعُ الْعَلِيمُ
                        </div>
                        <div class="azkar-benefit">
                            الفائدة: من قالها ثلاثاً لم تصبه فجأة بلاء حتى يمسي
                        </div>
                        <div style="text-align: center;">
                            <span class="counter" onclick="this.innerHTML = parseInt(this.innerHTML) > 0 ? parseInt(this.innerHTML) - 1 : 0">3</span>
                        </div>
                    </div>
                `;
            } else if (type === 'evening') {
                content.innerHTML = `
                    <h3>🌙 أذكار المساء</h3>
                    <div class="azkar-item">
                        <div class="azkar-text">
                            أَمْسَيْنَا وَأَمْسَى الْمُلْكُ لِلهِ وَالْحَمْدُ لِلهِ لاَ إِلَهَ إِلاَّ اللهُ وَحْدَهُ لاَ شَرِيكَ لَهُ
                        </div>
                        <div class="azkar-benefit">
                            الفائدة: دعاء شامل للحماية والخير
                        </div>
                        <div style="text-align: center;">
                            <span class="counter" onclick="this.innerHTML = parseInt(this.innerHTML) > 0 ? parseInt(this.innerHTML) - 1 : 0">1</span>
                        </div>
                    </div>
                    <div class="azkar-item">
                        <div class="azkar-text">
                            اللَّهُمَّ بِكَ أَمْسَيْنَا وَبِكَ أَصْبَحْنَا وَبِكَ نَحْيَا وَبِكَ نَمُوتُ وَإِلَيْكَ الْمَصِيرُ
                        </div>
                        <div class="azkar-benefit">
                            الفائدة: إقرار بالتوحيد والتوكل على الله
                        </div>
                        <div style="text-align: center;">
                            <span class="counter" onclick="this.innerHTML = parseInt(this.innerHTML) > 0 ? parseInt(this.innerHTML) - 1 : 0">1</span>
                        </div>
                    </div>
                `;
            }
        }
        
        // إظهار رسالة ترحيب
        setTimeout(() => {
            alert('🌙 أهلاً وسهلاً بك في تطبيق نور الإيمان!\n\nيمكنك التنقل بين الأقسام والاستفادة من المحتوى الإسلامي المتنوع.\n\nبارك الله فيك! 🤲');
        }, 1000);
    </script>
</body>
</html>
