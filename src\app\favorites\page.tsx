'use client'

import { useState } from 'react'
import { Heart, BookOpen, Volume2, Share2, Trash2, Search, Filter } from 'lucide-react'
import { useFavorites, FavoriteItem } from '@/hooks/useFavorites'

export default function FavoritesPage() {
  const { favorites, removeFromFavorites, clearAllFavorites } = useFavorites()
  const [searchTerm, setSearchTerm] = useState('')
  const [filterType, setFilterType] = useState<'all' | FavoriteItem['type']>('all')

  // تصفية المفضلة
  const filteredFavorites = favorites.filter(item => {
    const matchesSearch = item.title.includes(searchTerm) || item.content.includes(searchTerm)
    const matchesType = filterType === 'all' || item.type === filterType
    return matchesSearch && matchesType
  })

  const handleShare = (item: FavoriteItem) => {
    if (navigator.share) {
      navigator.share({
        title: item.title,
        text: item.content,
        url: window.location.href
      })
    } else {
      navigator.clipboard.writeText(item.content)
      alert('تم نسخ المحتوى إلى الحافظة')
    }
  }

  const getTypeLabel = (type: FavoriteItem['type']) => {
    switch (type) {
      case 'ayah': return 'آية'
      case 'zikr': return 'ذكر'
      case 'surah': return 'سورة'
      default: return type
    }
  }

  const getTypeColor = (type: FavoriteItem['type']) => {
    switch (type) {
      case 'ayah': return 'bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400'
      case 'zikr': return 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400'
      case 'surah': return 'bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-400'
      default: return 'bg-gray-100 dark:bg-gray-900 text-gray-600 dark:text-gray-400'
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center py-6">
        <h1 className="text-3xl md:text-4xl font-bold text-gray-800 dark:text-white mb-4">
          المفضلة
        </h1>
        <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
          الآيات والأذكار والسور التي أضفتها إلى مفضلتك
        </p>
      </div>

      {/* Search and Filter */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <div className="flex flex-col md:flex-row gap-4">
          {/* Search */}
          <div className="flex-1 relative">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
            <input
              type="text"
              placeholder="ابحث في المفضلة..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pr-10 pl-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            />
          </div>

          {/* Filter */}
          <div className="flex items-center space-x-2 space-x-reverse">
            <Filter className="h-5 w-5 text-gray-600 dark:text-gray-400" />
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value as typeof filterType)}
              className="px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            >
              <option value="all">جميع الأنواع</option>
              <option value="ayah">الآيات</option>
              <option value="zikr">الأذكار</option>
              <option value="surah">السور</option>
            </select>
          </div>

          {/* Clear All */}
          {favorites.length > 0 && (
            <button
              onClick={() => {
                if (confirm('هل أنت متأكد من حذف جميع المفضلة؟')) {
                  clearAllFavorites()
                }
              }}
              className="px-4 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors duration-200"
            >
              حذف الكل
            </button>
          )}
        </div>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 text-center">
          <div className="text-2xl font-bold text-blue-600 dark:text-blue-400 mb-2">
            {favorites.length}
          </div>
          <p className="text-gray-600 dark:text-gray-400">إجمالي المفضلة</p>
        </div>
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 text-center">
          <div className="text-2xl font-bold text-green-600 dark:text-green-400 mb-2">
            {favorites.filter(f => f.type === 'ayah').length}
          </div>
          <p className="text-gray-600 dark:text-gray-400">الآيات</p>
        </div>
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 text-center">
          <div className="text-2xl font-bold text-purple-600 dark:text-purple-400 mb-2">
            {favorites.filter(f => f.type === 'zikr').length}
          </div>
          <p className="text-gray-600 dark:text-gray-400">الأذكار</p>
        </div>
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 text-center">
          <div className="text-2xl font-bold text-orange-600 dark:text-orange-400 mb-2">
            {favorites.filter(f => f.type === 'surah').length}
          </div>
          <p className="text-gray-600 dark:text-gray-400">السور</p>
        </div>
      </div>

      {/* Favorites List */}
      {filteredFavorites.length === 0 ? (
        <div className="text-center py-12">
          <Heart className="h-16 w-16 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-gray-600 dark:text-gray-400 mb-2">
            {favorites.length === 0 ? 'لا توجد مفضلة بعد' : 'لا توجد نتائج'}
          </h3>
          <p className="text-gray-500 dark:text-gray-500">
            {favorites.length === 0 
              ? 'ابدأ بإضافة الآيات والأذكار المفضلة لديك'
              : 'جرب تغيير مصطلحات البحث أو الفلتر'
            }
          </p>
        </div>
      ) : (
        <div className="space-y-4">
          {filteredFavorites.map((item) => (
            <div
              key={`${item.type}-${item.id}`}
              className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6"
            >
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 space-x-reverse mb-3">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getTypeColor(item.type)}`}>
                      {getTypeLabel(item.type)}
                    </span>
                    <h3 className="font-semibold text-gray-800 dark:text-white">
                      {item.title}
                    </h3>
                  </div>
                  
                  <div className="arabic-text text-lg leading-relaxed text-gray-800 dark:text-white mb-3">
                    {item.content}
                  </div>
                  
                  {item.source && (
                    <p className="text-sm text-blue-600 dark:text-blue-400">
                      <strong>المصدر:</strong> {item.source}
                    </p>
                  )}
                  
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                    أضيف في: {new Date(item.dateAdded).toLocaleDateString('ar-SA')}
                  </p>
                </div>

                <div className="flex items-center space-x-2 space-x-reverse mr-4">
                  <button
                    onClick={() => handleShare(item)}
                    className="p-2 bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 rounded-lg hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors duration-200"
                    title="مشاركة"
                  >
                    <Share2 className="h-4 w-4" />
                  </button>
                  <button
                    className="p-2 bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400 rounded-lg hover:bg-green-200 dark:hover:bg-green-800 transition-colors duration-200"
                    title="استمع"
                  >
                    <Volume2 className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => removeFromFavorites(item.id, item.type)}
                    className="p-2 bg-red-100 dark:bg-red-900 text-red-600 dark:text-red-400 rounded-lg hover:bg-red-200 dark:hover:bg-red-800 transition-colors duration-200"
                    title="حذف من المفضلة"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Export Options */}
      {favorites.length > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">
            خيارات التصدير
          </h3>
          <div className="flex flex-wrap gap-3">
            <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200">
              تصدير كملف نصي
            </button>
            <button className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200">
              تصدير كـ PDF
            </button>
            <button className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors duration-200">
              مشاركة المجموعة
            </button>
          </div>
        </div>
      )}
    </div>
  )
}
