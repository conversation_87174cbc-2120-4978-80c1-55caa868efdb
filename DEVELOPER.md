# دليل المطور - نور الإيمان

## 🏗️ هيكل المشروع

```
noor-al-iman/
├── src/
│   ├── app/                    # صفحات التطبيق (App Router)
│   │   ├── globals.css         # الأنماط العامة
│   │   ├── layout.tsx          # التخطيط الرئيسي
│   │   ├── page.tsx            # الصفحة الرئيسية
│   │   ├── quran/              # صفحة القرآن الكريم
│   │   ├── prayer-times/       # صفحة أوقات الصلاة
│   │   ├── azkar/              # صفحة الأذكار
│   │   ├── favorites/          # صفحة المفضلة
│   │   ├── stats/              # صفحة الإحصائيات
│   │   └── settings/           # صفحة الإعدادات
│   ├── components/             # المكونات المشتركة
│   │   ├── Navigation.tsx      # شريط التنقل
│   │   ├── ThemeProvider.tsx   # مزود الثيم
│   │   ├── PrayerTimes.tsx     # مكون أوقات الصلاة
│   │   ├── QuranVerse.tsx      # مكون آية اليوم
│   │   ├── SurahReader.tsx     # قارئ السور
│   │   ├── AzkarReader.tsx     # قارئ الأذكار
│   │   ├── QiblaCompass.tsx    # بوصلة القبلة
│   │   └── NotificationSettings.tsx # إعدادات التنبيهات
│   ├── data/                   # بيانات التطبيق
│   │   ├── quran.ts            # بيانات القرآن الكريم
│   │   └── azkar.ts            # بيانات الأذكار
│   ├── hooks/                  # React Hooks مخصصة
│   │   ├── useLocalStorage.ts  # hook للتخزين المحلي
│   │   ├── useFavorites.ts     # hook للمفضلة
│   │   └── useProgress.ts      # hook لتتبع التقدم
│   └── services/               # خدمات التطبيق
│       ├── prayerTimes.ts      # خدمة أوقات الصلاة
│       └── notifications.ts    # خدمة التنبيهات
├── public/                     # الملفات العامة
│   ├── manifest.json           # ملف PWA
│   └── icons/                  # أيقونات التطبيق
├── tailwind.config.js          # إعدادات Tailwind
├── next.config.js              # إعدادات Next.js
└── package.json                # تبعيات المشروع
```

## 🛠️ التقنيات المستخدمة

### Frontend Framework
- **Next.js 14**: إطار عمل React مع App Router
- **TypeScript**: للكتابة الآمنة والتطوير المتقدم
- **React 18**: مكتبة واجهة المستخدم

### Styling
- **Tailwind CSS**: إطار عمل CSS utility-first
- **CSS Variables**: للثيمات الديناميكية
- **Responsive Design**: تصميم متجاوب لجميع الأجهزة

### State Management
- **React Hooks**: useState, useEffect, useContext
- **Local Storage**: للتخزين المحلي
- **Custom Hooks**: hooks مخصصة للمنطق المشترك

### Icons & Fonts
- **Lucide React**: مكتبة أيقونات حديثة
- **Google Fonts**: خط أميري للنصوص العربية

## 🔧 إعداد بيئة التطوير

### المتطلبات
- Node.js 18+ 
- npm أو yarn أو pnpm
- Git

### خطوات الإعداد

1. **استنساخ المشروع**
```bash
git clone https://github.com/your-username/noor-al-iman.git
cd noor-al-iman
```

2. **تثبيت التبعيات**
```bash
npm install
```

3. **تشغيل التطبيق**
```bash
npm run dev
```

4. **فتح المتصفح**
```
http://localhost:3000
```

## 📝 أوامر التطوير

```bash
# تشغيل التطبيق في وضع التطوير
npm run dev

# بناء التطبيق للإنتاج
npm run build

# تشغيل التطبيق المبني
npm start

# فحص الكود
npm run lint

# إصلاح مشاكل الكود تلقائياً
npm run lint:fix

# فحص الأنواع TypeScript
npm run type-check
```

## 🎨 نظام التصميم

### الألوان
```css
/* الألوان الأساسية */
--primary: #2563eb;      /* أزرق */
--secondary: #10b981;    /* أخضر */
--accent: #8b5cf6;       /* بنفسجي */
--warning: #f59e0b;      /* أصفر */
--error: #ef4444;        /* أحمر */

/* الألوان الرمادية */
--gray-50: #f9fafb;
--gray-100: #f3f4f6;
--gray-900: #111827;
```

### الخطوط
```css
/* الخط الأساسي */
font-family: 'Amiri', serif;

/* أحجام الخطوط */
--text-xs: 0.75rem;      /* 12px */
--text-sm: 0.875rem;     /* 14px */
--text-base: 1rem;       /* 16px */
--text-lg: 1.125rem;     /* 18px */
--text-xl: 1.25rem;      /* 20px */
--text-2xl: 1.5rem;      /* 24px */
--text-3xl: 1.875rem;    /* 30px */
--text-4xl: 2.25rem;     /* 36px */
```

### المسافات
```css
/* نظام المسافات (8px base) */
--space-1: 0.25rem;      /* 4px */
--space-2: 0.5rem;       /* 8px */
--space-3: 0.75rem;      /* 12px */
--space-4: 1rem;         /* 16px */
--space-6: 1.5rem;       /* 24px */
--space-8: 2rem;         /* 32px */
```

## 🧩 إضافة مكونات جديدة

### مثال: إنشاء مكون جديد

```typescript
// src/components/NewComponent.tsx
'use client'

import { useState } from 'react'
import { Icon } from 'lucide-react'

interface NewComponentProps {
  title: string
  onAction?: () => void
}

export function NewComponent({ title, onAction }: NewComponentProps) {
  const [isActive, setIsActive] = useState(false)

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
      <h2 className="text-xl font-bold text-gray-800 dark:text-white mb-4">
        {title}
      </h2>
      
      <button
        onClick={() => {
          setIsActive(!isActive)
          onAction?.()
        }}
        className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200"
      >
        {isActive ? 'مفعل' : 'غير مفعل'}
      </button>
    </div>
  )
}
```

## 📊 إضافة بيانات جديدة

### مثال: إضافة سورة جديدة

```typescript
// src/data/quran.ts
export const newSurah: Surah = {
  number: 115,
  name: 'السورة الجديدة',
  englishName: 'New Surah',
  englishTranslation: 'The New Chapter',
  numberOfAyahs: 10,
  revelationType: 'Meccan',
  revelationTypeArabic: 'مكية'
}

// إضافة السورة للقائمة
export const surahs: Surah[] = [
  // ... السور الموجودة
  newSurah
]
```

## 🔧 إضافة خدمات جديدة

### مثال: خدمة جديدة

```typescript
// src/services/newService.ts
export class NewService {
  private static instance: NewService

  private constructor() {}

  static getInstance(): NewService {
    if (!NewService.instance) {
      NewService.instance = new NewService()
    }
    return NewService.instance
  }

  async performAction(data: any): Promise<any> {
    try {
      // منطق الخدمة
      return { success: true, data }
    } catch (error) {
      console.error('Error in NewService:', error)
      throw error
    }
  }
}

export const newService = NewService.getInstance()
```

## 🎯 أفضل الممارسات

### 1. تسمية الملفات
- استخدم PascalCase للمكونات: `MyComponent.tsx`
- استخدم camelCase للخدمات: `myService.ts`
- استخدم kebab-case للصفحات: `my-page/page.tsx`

### 2. تنظيم الكود
- ضع المنطق المعقد في hooks مخصصة
- استخدم TypeScript للتحقق من الأنواع
- اكتب تعليقات واضحة باللغة العربية

### 3. الأداء
- استخدم `'use client'` فقط عند الحاجة
- استخدم `React.memo` للمكونات الثقيلة
- تجنب re-renders غير الضرورية

### 4. إمكانية الوصول
- استخدم semantic HTML
- أضف `alt` للصور
- استخدم `aria-label` للأزرار

## 🧪 الاختبار

### إضافة اختبارات جديدة

```typescript
// __tests__/components/NewComponent.test.tsx
import { render, screen } from '@testing-library/react'
import { NewComponent } from '@/components/NewComponent'

describe('NewComponent', () => {
  it('renders correctly', () => {
    render(<NewComponent title="Test Title" />)
    expect(screen.getByText('Test Title')).toBeInTheDocument()
  })
})
```

## 🚀 النشر

### بناء للإنتاج
```bash
npm run build
```

### متغيرات البيئة
```bash
# .env.local
NEXT_PUBLIC_APP_VERSION=1.0.0
NEXT_PUBLIC_API_URL=https://api.example.com
```

## 🤝 المساهمة

1. Fork المشروع
2. إنشاء branch جديد (`git checkout -b feature/amazing-feature`)
3. Commit التغييرات (`git commit -m 'Add amazing feature'`)
4. Push إلى Branch (`git push origin feature/amazing-feature`)
5. فتح Pull Request

## 📞 الدعم

- **الوثائق**: راجع ملف README.md
- **المشاكل**: افتح issue في GitHub
- **الاقتراحات**: استخدم Discussions في GitHub

---

**بارك الله فيكم على مساهماتكم في هذا المشروع الخيري** 🤲
