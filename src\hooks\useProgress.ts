'use client'

import { useLocalStorage } from './useLocalStorage'

export interface ReadingProgress {
  surahNumber: number
  ayahNumber: number
  lastRead: string
  totalTime: number // in minutes
}

export interface AzkarProgress {
  categoryId: string
  date: string
  completed: boolean
  counters: { [zikrId: number]: number }
  completedAt?: string
}

export interface UserProgress {
  quran: {
    lastRead?: ReadingProgress
    bookmarks: ReadingProgress[]
    totalReadingTime: number
  }
  azkar: {
    daily: AzkarProgress[]
    streakDays: number
    lastCompletedDate?: string
  }
  prayerTimes: {
    notifications: {
      [prayer: string]: boolean
    }
    preferredLocation: string
    calculationMethod: string
  }
}

const defaultProgress: UserProgress = {
  quran: {
    bookmarks: [],
    totalReadingTime: 0
  },
  azkar: {
    daily: [],
    streakDays: 0
  },
  prayerTimes: {
    notifications: {
      fajr: true,
      dhuhr: true,
      asr: true,
      maghrib: true,
      isha: true
    },
    preferredLocation: 'الرياض',
    calculationMethod: 'mwl'
  }
}

export function useProgress() {
  const [progress, setProgress] = useLocalStorage<UserProgress>('noor-al-iman-progress', defaultProgress)

  // Quran Progress
  const updateLastRead = (surahNumber: number, ayahNumber: number) => {
    const newProgress: ReadingProgress = {
      surahNumber,
      ayahNumber,
      lastRead: new Date().toISOString(),
      totalTime: 0
    }
    
    setProgress(prev => ({
      ...prev,
      quran: {
        ...prev.quran,
        lastRead: newProgress
      }
    }))
  }

  const addBookmark = (surahNumber: number, ayahNumber: number) => {
    const bookmark: ReadingProgress = {
      surahNumber,
      ayahNumber,
      lastRead: new Date().toISOString(),
      totalTime: 0
    }

    setProgress(prev => ({
      ...prev,
      quran: {
        ...prev.quran,
        bookmarks: [...prev.quran.bookmarks.filter(b => 
          !(b.surahNumber === surahNumber && b.ayahNumber === ayahNumber)
        ), bookmark]
      }
    }))
  }

  const removeBookmark = (surahNumber: number, ayahNumber: number) => {
    setProgress(prev => ({
      ...prev,
      quran: {
        ...prev.quran,
        bookmarks: prev.quran.bookmarks.filter(b => 
          !(b.surahNumber === surahNumber && b.ayahNumber === ayahNumber)
        )
      }
    }))
  }

  const isBookmarked = (surahNumber: number, ayahNumber: number) => {
    return progress.quran.bookmarks.some(b => 
      b.surahNumber === surahNumber && b.ayahNumber === ayahNumber
    )
  }

  // Azkar Progress
  const updateAzkarProgress = (categoryId: string, counters: { [zikrId: number]: number }) => {
    const today = new Date().toISOString().split('T')[0]
    const isCompleted = Object.values(counters).every(count => count === 0)
    
    setProgress(prev => {
      const existingIndex = prev.azkar.daily.findIndex(
        p => p.categoryId === categoryId && p.date === today
      )
      
      const newProgress: AzkarProgress = {
        categoryId,
        date: today,
        completed: isCompleted,
        counters,
        completedAt: isCompleted ? new Date().toISOString() : undefined
      }
      
      let newDaily = [...prev.azkar.daily]
      if (existingIndex >= 0) {
        newDaily[existingIndex] = newProgress
      } else {
        newDaily.push(newProgress)
      }
      
      // Calculate streak
      let newStreak = prev.azkar.streakDays
      if (isCompleted) {
        const yesterday = new Date()
        yesterday.setDate(yesterday.getDate() - 1)
        const yesterdayStr = yesterday.toISOString().split('T')[0]
        
        if (prev.azkar.lastCompletedDate === yesterdayStr || !prev.azkar.lastCompletedDate) {
          newStreak = prev.azkar.streakDays + 1
        } else {
          newStreak = 1
        }
      }
      
      return {
        ...prev,
        azkar: {
          ...prev.azkar,
          daily: newDaily,
          streakDays: newStreak,
          lastCompletedDate: isCompleted ? today : prev.azkar.lastCompletedDate
        }
      }
    })
  }

  const getAzkarProgress = (categoryId: string, date?: string) => {
    const targetDate = date || new Date().toISOString().split('T')[0]
    return progress.azkar.daily.find(p => p.categoryId === categoryId && p.date === targetDate)
  }

  const getTodayCompletedAzkar = () => {
    const today = new Date().toISOString().split('T')[0]
    return progress.azkar.daily.filter(p => p.date === today && p.completed)
  }

  // Prayer Times Settings
  const updatePrayerNotification = (prayer: string, enabled: boolean) => {
    setProgress(prev => ({
      ...prev,
      prayerTimes: {
        ...prev.prayerTimes,
        notifications: {
          ...prev.prayerTimes.notifications,
          [prayer]: enabled
        }
      }
    }))
  }

  const updatePrayerSettings = (location: string, method: string) => {
    setProgress(prev => ({
      ...prev,
      prayerTimes: {
        ...prev.prayerTimes,
        preferredLocation: location,
        calculationMethod: method
      }
    }))
  }

  // Statistics
  const getStatistics = () => {
    const today = new Date().toISOString().split('T')[0]
    const todayAzkar = progress.azkar.daily.filter(p => p.date === today)
    const completedToday = todayAzkar.filter(p => p.completed).length
    
    return {
      quran: {
        totalBookmarks: progress.quran.bookmarks.length,
        totalReadingTime: progress.quran.totalReadingTime,
        lastRead: progress.quran.lastRead
      },
      azkar: {
        streakDays: progress.azkar.streakDays,
        completedToday,
        totalCategories: 4, // Based on our azkar categories
        completionRate: Math.round((completedToday / 4) * 100)
      }
    }
  }

  const resetProgress = () => {
    setProgress(defaultProgress)
  }

  return {
    progress,
    // Quran
    updateLastRead,
    addBookmark,
    removeBookmark,
    isBookmarked,
    // Azkar
    updateAzkarProgress,
    getAzkarProgress,
    getTodayCompletedAzkar,
    // Prayer Times
    updatePrayerNotification,
    updatePrayerSettings,
    // Statistics
    getStatistics,
    resetProgress
  }
}
