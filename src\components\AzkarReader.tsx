'use client'

import { useState, useEffect } from 'react'
import { ArrowRight, Volume2, RotateCcw, Share2, Heart, Play, Pause, Settings } from 'lucide-react'
import { AzkarCategory, Zikr } from '@/data/azkar'

interface AzkarReaderProps {
  category: AzkarCategory
  onBack: () => void
}

export function AzkarReader({ category, onBack }: AzkarReaderProps) {
  const [currentZikrIndex, setCurrentZikrIndex] = useState(0)
  const [counters, setCounters] = useState<{ [key: number]: number }>({})
  const [isPlaying, setIsPlaying] = useState(false)
  const [autoPlay, setAutoPlay] = useState(false)
  const [fontSize, setFontSize] = useState(20)
  const [showSettings, setShowSettings] = useState(false)

  // تهيئة العدادات
  useEffect(() => {
    const initialCounters: { [key: number]: number } = {}
    category.azkar.forEach(zikr => {
      initialCounters[zikr.id] = zikr.count
    })
    setCounters(initialCounters)
  }, [category])

  // تقليل العداد
  const decrementCounter = (zikrId: number) => {
    setCounters(prev => ({
      ...prev,
      [zikrId]: Math.max(0, prev[zikrId] - 1)
    }))
  }

  // إعادة تعيين العداد
  const resetCounter = (zikrId: number, originalCount: number) => {
    setCounters(prev => ({
      ...prev,
      [zikrId]: originalCount
    }))
  }

  // إعادة تعيين جميع العدادات
  const resetAllCounters = () => {
    const resetCounters: { [key: number]: number } = {}
    category.azkar.forEach(zikr => {
      resetCounters[zikr.id] = zikr.count
    })
    setCounters(resetCounters)
  }

  // حساب التقدم الإجمالي
  const calculateProgress = () => {
    const totalOriginal = category.azkar.reduce((sum, zikr) => sum + zikr.count, 0)
    const totalRemaining = Object.values(counters).reduce((sum, count) => sum + count, 0)
    return Math.round(((totalOriginal - totalRemaining) / totalOriginal) * 100)
  }

  // التشغيل التلقائي
  const handleAutoPlay = () => {
    setAutoPlay(!autoPlay)
    // في التطبيق الحقيقي سنضيف تشغيل صوتي
  }

  // مشاركة الذكر
  const handleShare = (zikr: Zikr) => {
    if (navigator.share) {
      navigator.share({
        title: `ذكر من ${category.name}`,
        text: zikr.text,
        url: window.location.href
      })
    } else {
      // نسخ إلى الحافظة
      navigator.clipboard.writeText(zikr.text)
      alert('تم نسخ الذكر إلى الحافظة')
    }
  }

  // إضافة إلى المفضلة
  const handleFavorite = (zikr: Zikr) => {
    // في التطبيق الحقيقي سنحفظ في قاعدة البيانات
    alert(`تم إضافة الذكر إلى المفضلة`)
  }

  const progress = calculateProgress()

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <div className="flex items-center justify-between mb-4">
          <button
            onClick={onBack}
            className="flex items-center space-x-2 space-x-reverse text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300"
          >
            <ArrowRight className="h-5 w-5" />
            <span>العودة للفهرس</span>
          </button>

          <div className="flex items-center space-x-2 space-x-reverse">
            <button
              onClick={handleAutoPlay}
              className={`p-2 rounded-lg transition-colors duration-200 ${
                autoPlay 
                  ? 'bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400' 
                  : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400'
              }`}
            >
              {isPlaying ? <Pause className="h-5 w-5" /> : <Play className="h-5 w-5" />}
            </button>
            <button
              onClick={() => setShowSettings(!showSettings)}
              className="p-2 bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 rounded-lg hover:bg-blue-200 dark:hover:bg-blue-800"
            >
              <Settings className="h-5 w-5" />
            </button>
          </div>
        </div>

        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-800 dark:text-white mb-2">
            {category.name}
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            {category.description}
          </p>
          {category.time && (
            <p className="text-sm text-blue-600 dark:text-blue-400">
              الوقت المستحب: {category.time}
            </p>
          )}
        </div>

        {/* Progress Bar */}
        <div className="mt-6">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm text-gray-600 dark:text-gray-400">التقدم</span>
            <span className="text-sm font-semibold text-gray-800 dark:text-white">{progress}%</span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div 
              className="bg-green-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${progress}%` }}
            ></div>
          </div>
        </div>
      </div>

      {/* Settings Panel */}
      {showSettings && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">إعدادات القراءة</h3>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                حجم الخط
              </label>
              <div className="flex items-center space-x-4 space-x-reverse">
                <span className="text-sm text-gray-600 dark:text-gray-400">صغير</span>
                <input
                  type="range"
                  min="16"
                  max="32"
                  value={fontSize}
                  onChange={(e) => setFontSize(Number(e.target.value))}
                  className="flex-1"
                />
                <span className="text-sm text-gray-600 dark:text-gray-400">كبير</span>
                <span className="text-sm font-medium text-gray-800 dark:text-white w-12">
                  {fontSize}px
                </span>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-gray-700 dark:text-gray-300">التشغيل التلقائي</span>
              <label className="relative inline-flex items-center cursor-pointer">
                <input 
                  type="checkbox" 
                  checked={autoPlay}
                  onChange={handleAutoPlay}
                  className="sr-only peer" 
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
              </label>
            </div>
          </div>
        </div>
      )}

      {/* Azkar List */}
      <div className="space-y-4">
        {category.azkar.map((zikr, index) => (
          <div
            key={zikr.id}
            className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden"
          >
            <div className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <div 
                    className="arabic-text leading-relaxed text-gray-800 dark:text-white mb-4 cursor-pointer"
                    style={{ fontSize: `${fontSize}px` }}
                    onClick={() => decrementCounter(zikr.id)}
                  >
                    {zikr.text}
                  </div>
                  
                  <div className="space-y-2 text-sm">
                    <div className="text-gray-600 dark:text-gray-400">
                      <strong>الفائدة:</strong> {zikr.benefit}
                    </div>
                    <div className="text-blue-600 dark:text-blue-400">
                      <strong>المصدر:</strong> {zikr.source}
                    </div>
                  </div>
                </div>

                <div className="flex flex-col items-center space-y-2 mr-4">
                  {/* Counter */}
                  <div className="text-center">
                    <div 
                      className={`w-16 h-16 rounded-full flex items-center justify-center text-xl font-bold cursor-pointer transition-all duration-200 ${
                        counters[zikr.id] === 0
                          ? 'bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400'
                          : 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 hover:scale-110'
                      }`}
                      onClick={() => decrementCounter(zikr.id)}
                    >
                      {counters[zikr.id]}
                    </div>
                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      من {zikr.count}
                    </p>
                  </div>

                  {/* Reset Button */}
                  <button
                    onClick={() => resetCounter(zikr.id, zikr.count)}
                    className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                    title="إعادة تعيين"
                  >
                    <RotateCcw className="h-4 w-4" />
                  </button>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700">
                <div className="flex items-center space-x-2 space-x-reverse">
                  <button
                    onClick={() => handleShare(zikr)}
                    className="flex items-center space-x-1 space-x-reverse px-3 py-1 bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 rounded-md hover:bg-blue-200 dark:hover:bg-blue-800 text-sm"
                  >
                    <Share2 className="h-3 w-3" />
                    <span>مشاركة</span>
                  </button>
                  <button
                    onClick={() => handleFavorite(zikr)}
                    className="flex items-center space-x-1 space-x-reverse px-3 py-1 bg-red-100 dark:bg-red-900 text-red-600 dark:text-red-400 rounded-md hover:bg-red-200 dark:hover:bg-red-800 text-sm"
                  >
                    <Heart className="h-3 w-3" />
                    <span>مفضلة</span>
                  </button>
                </div>

                <button className="flex items-center space-x-1 space-x-reverse px-3 py-1 bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400 rounded-md hover:bg-green-200 dark:hover:bg-green-800 text-sm">
                  <Volume2 className="h-3 w-3" />
                  <span>استمع</span>
                </button>
              </div>
            </div>

            {/* Progress for this Zikr */}
            {zikr.count > 1 && (
              <div className="px-6 pb-4">
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1">
                  <div 
                    className="bg-green-600 h-1 rounded-full transition-all duration-300"
                    style={{ width: `${((zikr.count - counters[zikr.id]) / zikr.count) * 100}%` }}
                  ></div>
                </div>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Footer Actions */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <div className="flex justify-center space-x-4 space-x-reverse">
          <button
            onClick={resetAllCounters}
            className="flex items-center space-x-2 space-x-reverse px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200"
          >
            <RotateCcw className="h-4 w-4" />
            <span>إعادة تعيين الكل</span>
          </button>
          <button className="flex items-center space-x-2 space-x-reverse px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200">
            <Volume2 className="h-4 w-4" />
            <span>تشغيل الكل</span>
          </button>
        </div>
      </div>
    </div>
  )
}
